<?php

/**
 * Subscription Limits Management Class
 * Handles validation and enforcement of subscription-based limits
 */

require_once __DIR__ . '/config.php';

class SubscriptionLimits
{
    private $pdo;
    
    public function __construct()
    {
        $this->pdo = getPDOConnection();
    }
    
    /**
     * Get user's subscription limits
     */
    public function getUserLimits($userId)
    {
        try {
            $stmt = $this->pdo->prepare("
                SELECT 
                    u.id as user_id,
                    u.subscription_id,
                    sp.name as subscription_name,
                    sp.display_name_ar,
                    sp.max_products,
                    sp.max_landing_pages,
                    sp.max_storage_mb,
                    sp.max_templates,
                    sp.features
                FROM users u
                LEFT JOIN subscription_plans sp ON u.subscription_id = sp.id
                WHERE u.id = ? AND u.status = 'active'
            ");
            
            $stmt->execute([$userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$result) {
                throw new Exception('User not found or inactive');
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error getting user limits: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Get user's current usage statistics
     */
    public function getUserUsage($userId)
    {
        try {
            // Get user's store ID
            $stmt = $this->pdo->prepare("SELECT store_id FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                throw new Exception('User not found');
            }
            
            $storeId = $user['store_id'];
            
            // Count products
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as product_count 
                FROM produits 
                WHERE store_id = ? AND actif = 1
            ");
            $stmt->execute([$storeId]);
            $productCount = $stmt->fetchColumn();
            
            // Count landing pages
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as landing_page_count 
                FROM landing_pages 
                WHERE store_id = ?
            ");
            $stmt->execute([$storeId]);
            $landingPageCount = $stmt->fetchColumn();
            
            // Count categories (store-specific)
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) as category_count 
                FROM store_categories 
                WHERE store_id = ? AND is_active = 1
            ");
            $stmt->execute([$storeId]);
            $categoryCount = $stmt->fetchColumn();
            
            // Calculate storage usage (in MB)
            $stmt = $this->pdo->prepare("
                SELECT COALESCE(SUM(file_size), 0) as total_storage
                FROM product_images pi
                JOIN produits p ON pi.product_id = p.id
                WHERE p.store_id = ?
            ");
            $stmt->execute([$storeId]);
            $storageBytes = $stmt->fetchColumn();
            $storageMB = round($storageBytes / (1024 * 1024), 2);
            
            return [
                'products' => (int)$productCount,
                'landing_pages' => (int)$landingPageCount,
                'categories' => (int)$categoryCount,
                'storage_mb' => $storageMB
            ];
        } catch (Exception $e) {
            error_log("Error getting user usage: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Check if user can add a new product
     */
    public function canAddProduct($userId)
    {
        try {
            $limits = $this->getUserLimits($userId);
            $usage = $this->getUserUsage($userId);
            
            return [
                'allowed' => $usage['products'] < $limits['max_products'],
                'current' => $usage['products'],
                'limit' => $limits['max_products'],
                'remaining' => max(0, $limits['max_products'] - $usage['products'])
            ];
        } catch (Exception $e) {
            error_log("Error checking product limit: " . $e->getMessage());
            return ['allowed' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Check if user can add a new landing page
     */
    public function canAddLandingPage($userId)
    {
        try {
            $limits = $this->getUserLimits($userId);
            $usage = $this->getUserUsage($userId);
            
            return [
                'allowed' => $usage['landing_pages'] < $limits['max_landing_pages'],
                'current' => $usage['landing_pages'],
                'limit' => $limits['max_landing_pages'],
                'remaining' => max(0, $limits['max_landing_pages'] - $usage['landing_pages'])
            ];
        } catch (Exception $e) {
            error_log("Error checking landing page limit: " . $e->getMessage());
            return ['allowed' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Check if user can add a new category
     */
    public function canAddCategory($userId)
    {
        try {
            $limits = $this->getUserLimits($userId);
            $usage = $this->getUserUsage($userId);
            
            // For categories, we'll use a reasonable limit based on subscription
            $maxCategories = $this->getCategoryLimit($limits['subscription_name']);
            
            return [
                'allowed' => $usage['categories'] < $maxCategories,
                'current' => $usage['categories'],
                'limit' => $maxCategories,
                'remaining' => max(0, $maxCategories - $usage['categories'])
            ];
        } catch (Exception $e) {
            error_log("Error checking category limit: " . $e->getMessage());
            return ['allowed' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get category limit based on subscription
     */
    private function getCategoryLimit($subscriptionName)
    {
        $limits = [
            'free' => 3,
            'basic' => 10,
            'premium' => 25,
            'enterprise' => 50
        ];
        
        return $limits[$subscriptionName] ?? 5;
    }
    
    /**
     * Check storage limit
     */
    public function canUploadFile($userId, $fileSizeBytes)
    {
        try {
            $limits = $this->getUserLimits($userId);
            $usage = $this->getUserUsage($userId);
            
            $fileSizeMB = $fileSizeBytes / (1024 * 1024);
            $newTotal = $usage['storage_mb'] + $fileSizeMB;
            
            return [
                'allowed' => $newTotal <= $limits['max_storage_mb'],
                'current_mb' => $usage['storage_mb'],
                'limit_mb' => $limits['max_storage_mb'],
                'file_size_mb' => round($fileSizeMB, 2),
                'remaining_mb' => max(0, $limits['max_storage_mb'] - $usage['storage_mb'])
            ];
        } catch (Exception $e) {
            error_log("Error checking storage limit: " . $e->getMessage());
            return ['allowed' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get comprehensive limits and usage for a user
     */
    public function getUserLimitsAndUsage($userId)
    {
        try {
            $limits = $this->getUserLimits($userId);
            $usage = $this->getUserUsage($userId);
            
            return [
                'subscription' => [
                    'name' => $limits['subscription_name'],
                    'display_name' => $limits['display_name_ar']
                ],
                'limits' => [
                    'products' => $limits['max_products'],
                    'landing_pages' => $limits['max_landing_pages'],
                    'storage_mb' => $limits['max_storage_mb'],
                    'categories' => $this->getCategoryLimit($limits['subscription_name'])
                ],
                'usage' => $usage,
                'remaining' => [
                    'products' => max(0, $limits['max_products'] - $usage['products']),
                    'landing_pages' => max(0, $limits['max_landing_pages'] - $usage['landing_pages']),
                    'storage_mb' => max(0, $limits['max_storage_mb'] - $usage['storage_mb']),
                    'categories' => max(0, $this->getCategoryLimit($limits['subscription_name']) - $usage['categories'])
                ]
            ];
        } catch (Exception $e) {
            error_log("Error getting user limits and usage: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Validate action against subscription limits
     */
    public function validateAction($userId, $action, $additionalData = [])
    {
        try {
            switch ($action) {
                case 'add_product':
                    return $this->canAddProduct($userId);
                    
                case 'add_landing_page':
                    return $this->canAddLandingPage($userId);
                    
                case 'add_category':
                    return $this->canAddCategory($userId);
                    
                case 'upload_file':
                    $fileSize = $additionalData['file_size'] ?? 0;
                    return $this->canUploadFile($userId, $fileSize);
                    
                default:
                    throw new Exception('Unknown action: ' . $action);
            }
        } catch (Exception $e) {
            error_log("Error validating action: " . $e->getMessage());
            return ['allowed' => false, 'error' => $e->getMessage()];
        }
    }
}
