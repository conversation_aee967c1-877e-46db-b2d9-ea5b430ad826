<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <!-- Critical CSS for initial render -->
    <style>
      body {
        visibility: hidden;
      }
      .content-loaded {
        visibility: visible;
      }
      #loading-indicator {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        text-align: center;
        visibility: visible;
      }
      #loading-indicator .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>لوحة التحكم - متجر الكتب</title>
    <link rel="stylesheet" href="../css/style.css" />
    <link rel="stylesheet" href="css/admin.css" />
    <link rel="stylesheet" href="css/product-landing.css" />
    <link rel="stylesheet" href="css/landing-pages.css" />
    <link rel="stylesheet" href="css/ultimate-visibility-fix.css" />
    <link rel="stylesheet" href="css/admin-loading-fix.css" />

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <!-- TinyMCE will be loaded later -->
  </head>
  <body>
    <script src="js/emergency-loading-fix.js"></script>
    <div id="loading-indicator">
      <div class="spinner"></div>
      <div>جاري التحميل...</div>
    </div>
    <div class="notifications-container" id="notificationsContainer"></div>

    <!-- Mobile Menu Toggle -->
    <button class="mobile-menu-toggle" id="mobileMenuToggle">
      <i class="fas fa-bars"></i>
    </button>

    <div class="admin-container">
      <!-- Sidebar -->
      <aside class="sidebar" id="sidebar">
        <div class="logo">
          <h1>لوحة التحكم</h1>
        </div>
        <nav class="admin-nav">
          <ul>
            <li class="active" data-section="dashboard">
              <i class="fas fa-home"></i>
              <span>الرئيسية</span>
            </li>
            <li data-section="books">
              <i class="fas fa-box"></i>
              <span>إدارة المنتجات</span>
            </li>
            <li data-section="orders">
              <i class="fas fa-shopping-cart"></i>
              <span>الطلبات</span>
            </li>
            <li data-section="landingPages">
              <i class="fas fa-bullhorn"></i>
              <span>صفحات هبوط</span>
            </li>
            <li data-section="reports">
              <i class="fas fa-chart-bar"></i>
              <span>التقارير والإحصائيات</span>
            </li>
            <li data-section="settings">
              <i class="fas fa-cog"></i>
              <span>إعدادات النظام</span>
            </li>
            <li id="logoutBtn">
              <i class="fas fa-sign-out-alt"></i>
              <span>تسجيل الخروج</span>
            </li>
          </ul>
        </nav>
      </aside>

      <!-- Main Content -->
      <main class="main-content">
        <!-- Header with refresh button -->
        <div class="content-header">
          <h1 id="pageTitle">لوحة المعلومات</h1>
          <button
            id="refreshPageBtn"
            class="refresh-button"
            title="تحديث الصفحة"
          >
            <i class="fas fa-sync-alt"></i>
            <span>تحديث الصفحة</span>
          </button>
        </div>

        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
          <h2>لوحة المعلومات</h2>
          <div class="stats-grid">
            <div class="stat-card">
              <i class="fas fa-box"></i>
              <div class="stat-info">
                <h3>إجمالي المنتجات</h3>
                <p id="totalBooks">0</p>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-shopping-cart"></i>
              <div class="stat-info">
                <h3>الطلبات الجديدة</h3>
                <p id="newOrders">0</p>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-money-bill-wave"></i>
              <div class="stat-info">
                <h3>إجمالي المبيعات</h3>
                <p id="totalSales">0 دج</p>
              </div>
            </div>
            <div class="stat-card">
              <i class="fas fa-file-alt"></i>
              <div class="stat-info">
                <h3>إجمالي صفحات الهبوط</h3>
                <p id="totalLandingPages">0</p>
              </div>
            </div>
          </div>

          <div class="recent-orders">
            <h3>آخر الطلبات</h3>
            <div class="table-responsive">
              <table id="recentOrdersTable">
                <thead>
                  <tr>
                    <th>رقم الطلب</th>
                    <th>العميل</th>
                    <th>المبلغ</th>
                    <th>الحالة</th>
                    <th>التاريخ</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </section>

        <!-- Products Management Section -->
        <section id="books" class="content-section">
          <h2>إدارة المنتجات</h2>
          <div class="section-header">
            <button id="addProductBtn" class="action-button">
              <i class="fas fa-plus"></i> إضافة منتج جديد
            </button>
            <button
              id="deleteSelectedProductsBtn"
              class="action-button"
              style="background: #e74c3c; display: none"
            >
              <i class="fas fa-trash"></i> حذف المحدد (<span
                id="selectedProductsCount"
                >0</span
              >)
            </button>
          </div>

          <div class="table-responsive">
            <table id="booksTable">
              <thead>
                <tr>
                  <th>
                    <input
                      type="checkbox"
                      id="selectAllProducts"
                      title="تحديد الكل"
                    />
                  </th>
                  <th>الرقم</th>
                  <th>الصورة</th>
                  <th>العنوان</th>
                  <th>النوع</th>
                  <th>السعر</th>
                  <th>المخزون</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </section>

        <!-- Orders Management Section -->
        <section id="orders" class="content-section">
          <h2>إدارة الطلبات</h2>
          <div class="orders-filters">
            <select id="orderStatusFilter">
              <option value="all">جميع الطلبات</option>
              <option value="en_attente">قيد الانتظار</option>
              <option value="payé">تم الدفع</option>
              <option value="expédié">تم الشحن</option>
            </select>
          </div>

          <div class="table-responsive">
            <table id="ordersTable">
              <thead>
                <tr>
                  <th>رقم الطلب</th>
                  <th>العميل</th>
                  <th>التفاصيل</th>
                  <th>المبلغ</th>
                  <th>الحالة</th>
                  <th>التاريخ</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody></tbody>
            </table>
          </div>
        </section>

        <!-- Landing Pages Section -->
        <section id="landingPages" class="content-section">
          <h2>صفحات هبوط</h2>
          <div class="section-header">
            <button id="addLandingPageBtn" class="action-button" onclick="safeAddLandingPage()">
              <i class="fas fa-plus"></i> أَضف صفحة هبوط
            </button>
            <button
              id="testLandingPageBtn"
              class="action-button"
              style="background: #f59e0b; margin-left: 10px"
              onclick="safeTestLandingPageModal()"
            >
              <i class="fas fa-bug"></i> اختيار المودال
            </button>
            <button
              id="deleteSelectedLandingPagesBtn"
              class="action-button"
              style="background: #e74c3c; display: none"
            >
              <i class="fas fa-trash"></i> حذف المحدد (<span
                id="selectedLandingPagesCount"
                >0</span
              >)
            </button>
          </div>

          <!-- Bulk Selection Controls -->
          <div
            class="bulk-controls"
            style="margin: 15px 0; display: none"
            id="landingPagesBulkControls"
          >
            <label style="display: flex; align-items: center; gap: 8px">
              <input
                type="checkbox"
                id="selectAllLandingPages"
                title="تحديد الكل"
              />
              <span>تحديد الكل</span>
            </label>
          </div>

          <!-- Landing Pages Container -->
          <div id="landingPagesContainer" style="margin-top: 20px">
            <!-- Landing pages will be loaded here dynamically -->
          </div>
        </section>

        <!-- Reports and Analytics Section -->
        <section id="reports" class="content-section">
          <h2>التقارير والإحصائيات</h2>
          <div id="reportsContent">
            <!-- Reports content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل التقارير والإحصائيات...</p>
            </div>
          </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
          <div class="settings-header">
            <h2><i class="fas fa-cogs"></i> إعدادات النظام</h2>
            <p class="settings-subtitle">مركز التحكم الرئيسي لإدارة جميع وظائف النظام</p>
          </div>

          <div class="settings-grid-modern">
            <!-- Categories -->
            <div class="setting-card" data-section="categoriesManagement">
              <div class="setting-card-icon categories">
                <i class="fas fa-tags"></i>
              </div>
              <div class="setting-card-content">
                <h3>الفئات</h3>
                <p>إدارة فئات المنتجات</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Payment Settings -->
            <div class="setting-card" data-section="paymentSettings">
              <div class="setting-card-icon payment">
                <i class="fas fa-credit-card"></i>
              </div>
              <div class="setting-card-content">
                <h3>إعدادات الدفع</h3>
                <p>تكوين طرق الدفع</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- General Settings -->
            <div class="setting-card" data-section="generalSettings">
              <div class="setting-card-icon general">
                <i class="fas fa-cog"></i>
              </div>
              <div class="setting-card-content">
                <h3>إعدادات عامة</h3>
                <p>إعدادات النظام العامة</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Store Settings -->
            <div class="setting-card" data-section="storeSettings">
              <div class="setting-card-icon store">
                <i class="fas fa-store"></i>
              </div>
              <div class="setting-card-content">
                <h3>إعدادات المتجر</h3>
                <p>إعدادات المتجر والمنتجات</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- User Management -->
            <div class="setting-card" data-section="userManagement">
              <div class="setting-card-icon users">
                <i class="fas fa-users"></i>
              </div>
              <div class="setting-card-content">
                <h3>إدارة المستخدمين</h3>
                <p>إدارة حسابات المستخدمين</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Stores Management -->
            <div class="setting-card" data-section="storesManagement">
              <div class="setting-card-icon stores">
                <i class="fas fa-store-alt"></i>
              </div>
              <div class="setting-card-content">
                <h3>المتاجر</h3>
                <p>إدارة متاجر المستخدمين</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Roles Management -->
            <div class="setting-card" data-section="rolesManagement">
              <div class="setting-card-icon roles">
                <i class="fas fa-user-shield"></i>
              </div>
              <div class="setting-card-content">
                <h3>إدارة الأدوار</h3>
                <p>إدارة أدوار وصلاحيات المستخدمين</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Subscriptions Management -->
            <div class="setting-card" data-section="subscriptionsManagement">
              <div class="setting-card-icon subscriptions">
                <i class="fas fa-crown"></i>
              </div>
              <div class="setting-card-content">
                <h3>إدارة الاشتراكات</h3>
                <p>إدارة خطط وحدود الاشتراكات</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- Security Settings -->
            <div class="setting-card" data-section="securitySettings">
              <div class="setting-card-icon security">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="setting-card-content">
                <h3>الأمان</h3>
                <p>إعدادات الأمان والحماية</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>

            <!-- System Testing -->
            <div class="setting-card" data-section="systemTesting">
              <div class="setting-card-icon testing">
                <i class="fas fa-vial"></i>
              </div>
              <div class="setting-card-content">
                <h3>اختبار النظام</h3>
                <p>فحص شامل للنظام</p>
              </div>
              <div class="setting-card-arrow">
                <i class="fas fa-chevron-left"></i>
              </div>
            </div>
          </div>

          <!-- Landing Page Modal -->
          <div id="landingPageModal" class="modal">
            <div class="modal-content product-modal">
              <div class="modal-header">
                <h3>إضافة صفحة هبوط جديدة</h3>
                <button
                  type="button"
                  class="close-modal-btn"
                  onclick="safeLandingPagesCloseModal()"
                  style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                    float: left;
                  "
                >
                  ×
                </button>
              </div>

              <!-- Template Selection Step -->
              <div id="templateSelectionStep" class="modal-step active">
                <h4 style="margin-bottom: 20px; color: #667eea">
                  🎨 اختر قالب الصفحة
                </h4>
                <div
                  class="template-grid"
                  id="templateGrid"
                  style="
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 15px;
                    margin-bottom: 20px;
                  "
                >
                  <!-- Templates will be loaded here -->
                </div>
                <div class="step-actions" style="text-align: center; display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                  <button
                    type="button"
                    class="action-button"
                    onclick="safeLandingPagesNextStep()"
                    disabled
                    id="nextStepBtn"
                  >
                    التالي - إعداد المحتوى
                  </button>
                  <button
                    type="button"
                    class="cancel-button"
                    onclick="safeLandingPagesSkipTemplate()"
                    title="تخطي اختيار القالب والانتقال مباشرة لإنشاء المحتوى"
                  >
                    تخطي القالب - إنشاء مخصص
                  </button>
                  <button
                    type="button"
                    class="action-button"
                    onclick="debugModalSteps()"
                    style="background: #f59e0b;"
                    title="تصحيح خطوات المودال"
                  >
                    🔧 تصحيح
                  </button>
                </div>
              </div>

              <!-- Content Step -->
              <div id="contentStep" class="modal-step" style="display: none">
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                  "
                >
                  <h4 style="color: #667eea">📝 إعداد المحتوى والتخطيط</h4>
                  <button
                    type="button"
                    class="cancel-button"
                    onclick="safeLandingPagesPreviousStep()"
                  >
                    العودة للقوالب
                  </button>
                </div>

                <!-- Layout Controls -->
                <div
                  class="layout-controls"
                  style="
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                  "
                >
                  <h5 style="margin: 0 0 15px 0; color: #495057">
                    ⚙️ إعدادات التخطيط
                  </h5>
                  <div
                    style="
                      display: grid;
                      grid-template-columns: 1fr 1fr;
                      gap: 15px;
                    "
                  >
                    <div class="form-group">
                      <label>موضع الصور:</label>
                      <select
                        id="imagePosition"
                        name="image_position"
                        style="
                          width: 100%;
                          padding: 8px;
                          border: 1px solid #ddd;
                          border-radius: 4px;
                        "
                      >
                        <option value="left">يسار</option>
                        <option value="center" selected>وسط</option>
                        <option value="right">يمين</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label>ترتيب النصوص:</label>
                      <select
                        id="textPosition"
                        name="text_position"
                        style="
                          width: 100%;
                          padding: 8px;
                          border: 1px solid #ddd;
                          border-radius: 4px;
                        "
                      >
                        <option value="left">يسار</option>
                        <option value="center">وسط</option>
                        <option value="right">يمين</option>
                        <option value="split" selected>
                          مقسم (يمين ويسار)
                        </option>
                      </select>
                    </div>
                  </div>
                </div>

                <form id="landingPageForm" class="product-form">
                  <input type="hidden" id="selectedTemplate" name="template" />
                  <input type="hidden" id="landingPageId" name="id" />
                  <div class="form-group">
                    <label>اختر المنتج</label>
                    <select
                      id="productSelect"
                      name="productSelect"
                      required
                    ></select>
                    <div
                      id="productSelectionStatus"
                      class="product-selection-status"
                    ></div>
                  </div>
                  <div class="form-group">
                    <label>عنوان الصفحة</label>
                    <input
                      type="text"
                      id="landingPageTitle"
                      name="landingPageTitle"
                      required
                      placeholder="مثال: فن اللامبالاة - نهج معاكس للعيش حياة جيدة"
                    />
                  </div>
                  <div class="form-group">
                    <label>🖼️ صور العرض المتحركة (Carousel)</label>
                    <div
                      style="
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 8px;
                        margin-bottom: 15px;
                      "
                    >
                      <h4 style="margin: 0 0 10px 0; color: #667eea">
                        📁 رفع الصور من الجهاز
                      </h4>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          gap: 10px;
                          margin-bottom: 10px;
                        "
                      >
                        <input
                          type="file"
                          id="landingPageImages"
                          multiple
                          accept="image/*"
                          style="flex: 1"
                        />
                        <button
                          type="button"
                          id="clearImagesBtn"
                          onclick="safeLandingPagesClearAllImages()"
                          style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            padding: 8px 12px;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 12px;
                          "
                          title="مسح جميع الصور"
                        >
                          🗑️ مسح الكل
                        </button>
                      </div>
                      <small
                        style="color: #666; display: block; margin-bottom: 10px"
                      >
                        💡 اختر عدة صور لعرضها في شكل carousel متحرك في أعلى
                        الصفحة
                      </small>
                      <div id="imagePreview" class="image-preview"></div>
                    </div>

                    <div
                      style="
                        background: #f0f8ff;
                        padding: 15px;
                        border-radius: 8px;
                      "
                    >
                      <h4 style="margin: 0 0 10px 0; color: #667eea">
                        🌐 إضافة صور من الإنترنت
                      </h4>
                      <div id="urlImagesContainer">
                        <div
                          class="url-image-input"
                          style="
                            display: flex;
                            align-items: center;
                            margin-bottom: 10px;
                          "
                        >
                          <input
                            type="url"
                            name="imageUrls[]"
                            placeholder="https://images.unsplash.com/photo-example.jpg"
                            style="
                              flex: 1;
                              padding: 8px;
                              border: 1px solid #ddd;
                              border-radius: 4px;
                              margin-left: 10px;
                            "
                          />
                          <button
                            type="button"
                            onclick="addUrlImageInput()"
                            class="add-btn"
                            style="
                              background: #28a745;
                              color: white;
                              border: none;
                              padding: 8px 12px;
                              border-radius: 4px;
                              cursor: pointer;
                              margin-left: 5px;
                            "
                            title="إضافة صورة أخرى"
                          >
                            ➕
                          </button>
                        </div>
                      </div>
                      <small style="color: #666">
                        💡 يمكنك إضافة صور من مواقع مثل Unsplash, Pixabay, أو أي
                        رابط صورة صالح
                      </small>
                    </div>
                  </div>
                  <div
                    style="
                      display: grid;
                      grid-template-columns: 1fr 1fr;
                      gap: 20px;
                      margin-top: 20px;
                    "
                  >
                    <div class="form-group">
                      <label
                        style="
                          background: #e8f4fd;
                          padding: 10px;
                          border-radius: 5px;
                          display: block;
                          margin-bottom: 10px;
                        "
                      >
                        📝 المحتوى الأيمن (مميزات المنتج)
                      </label>
                      <small
                        style="color: #666; display: block; margin-bottom: 10px"
                      >
                        💡 أضف هنا: المميزات، الفوائد، المواصفات التقنية، ما
                        يميز المنتج
                      </small>
                      <textarea
                        id="rightContent"
                        name="rightContent"
                        class="tinymce"
                        placeholder="مثال:
🎯 لماذا هذا المنتج مميز؟
• مميزة رقم 1
• مميزة رقم 2
• مميزة رقم 3

💡 ما ستحصل عليه:
• فائدة 1
• فائدة 2"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label
                        style="
                          background: #fff2e8;
                          padding: 10px;
                          border-radius: 5px;
                          display: block;
                          margin-bottom: 10px;
                        "
                      >
                        📄 المحتوى الأيسر (تفاصيل إضافية)
                      </label>
                      <small
                        style="color: #666; display: block; margin-bottom: 10px"
                      >
                        💡 أضف هنا: معلومات عن الشركة، آراء العملاء، ضمانات،
                        عروض خاصة
                      </small>
                      <textarea
                        id="leftContent"
                        name="leftContent"
                        class="tinymce"
                        placeholder="مثال:
🏢 عن الشركة
معلومات عن الشركة المصنعة...

🌟 آراء العملاء
'منتج ممتاز، أنصح به بشدة' - أحمد محمد

🎁 عرض خاص
خصم 20% لفترة محدودة!"
                      ></textarea>
                    </div>
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="action-button">حفظ</button>
                    <button
                      type="button"
                      class="cancel-button"
                      onclick="safeLandingPagesCloseModal()"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
              <!-- End Content Step -->
            </div>
          </div>


        </section>

        <!-- Categories Management Section -->
        <section id="categoriesManagement" class="content-section">
          <div id="categoriesContent">
            <!-- Categories management content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الفئات...</p>
            </div>
          </div>
        </section>

        <!-- Payment Settings Section -->
        <section id="paymentSettings" class="content-section">
          <div id="paymentSettingsContent">
            <!-- Payment settings content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الدفع...</p>
            </div>
          </div>
        </section>

        <!-- General Settings Section -->
        <section id="generalSettings" class="content-section">
          <div id="generalSettingsContent">
            <!-- General settings content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل الإعدادات العامة...</p>
            </div>
          </div>
        </section>

        <!-- Store Settings Section -->
        <section id="storeSettings" class="content-section">
          <div id="storeSettingsContent">
            <!-- Store settings content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات المتجر...</p>
            </div>
          </div>
        </section>

        <!-- User Management Section -->
        <section id="userManagement" class="content-section">
          <div id="userManagementContent">
            <!-- User management content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة المستخدمين...</p>
            </div>
          </div>
        </section>

        <!-- Stores Management Section -->
        <section id="storesManagement" class="content-section">
          <div id="storesManagementContent">
            <!-- Stores management content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة المتاجر...</p>
            </div>
          </div>
        </section>

        <!-- Roles Management Section -->
        <section id="rolesManagement" class="content-section">
          <div id="rolesManagementContent">
            <!-- Roles management content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الأدوار...</p>
            </div>
          </div>
        </section>

        <!-- Subscriptions Management Section -->
        <section id="subscriptionsManagement" class="content-section">
          <div id="subscriptionsManagementContent">
            <!-- Subscriptions management content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الاشتراكات...</p>
            </div>
          </div>
        </section>

        <!-- Security Settings Section -->
        <section id="securitySettings" class="content-section">
          <div id="securitySettingsContent">
            <!-- Security settings content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الأمان...</p>
            </div>
          </div>
        </section>

        <!-- System Testing Section -->
        <section id="systemTesting" class="content-section">
          <div id="systemTestingContent">
            <!-- System testing content will be loaded here -->
            <div style="text-align: center; padding: 40px;">
              <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
              <p style="margin-top: 15px; color: #666;">جاري تحميل نظام الاختبار...</p>
            </div>
          </div>
        </section>
      </main>
    </div>

    <!-- Add/Edit Product Modal -->
    <div id="productModal" class="modal">
      <div class="modal-content product-modal">
        <span class="close">&times;</span>
        <h3 id="modalTitle">إضافة منتج جديد</h3>
        <form id="productForm" class="product-form">
          <div class="form-group">
            <label>نوع المنتج <span class="help-text">(يحدد الحقول المطلوبة)</span></label>
            <select id="productType" class="product-type-select" required>
              <option value="book">كتاب</option>
              <option value="backpack">حقيبة ظهر</option>
              <option value="laptop">حاسوب محمول</option>
              <option value="smartphone">هاتف ذكي</option>
              <option value="accessory">إكسسوار</option>
              <option value="sports">رياضة</option>
              <option value="beauty">تجميل</option>
              <option value="game">لعبة</option>
              <option value="clothing">ملابس</option>
              <option value="home">منزل</option>
            </select>
            <small class="field-help">اختر نوع المنتج لإظهار الحقول المناسبة (مؤلف للكتب، مواصفات للحاسوب، إلخ)</small>
          </div>

          <div class="form-group">
            <label>فئة التصنيف <span class="help-text">(للتنظيم والعرض)</span></label>
            <select id="productCategory" required>
              <option value="">اختر فئة التصنيف...</option>
              <!-- Categories will be loaded dynamically -->
            </select>
            <small class="field-help">اختر الفئة التي ينتمي إليها المنتج لتنظيم العرض في المتجر</small>
          </div>

          <!-- Common Fields -->
          <div class="form-group">
            <label>عنوان المنتج</label>
            <input type="text" id="productTitle" required />
          </div>
          <div class="form-group">
            <label>الوصف</label>
            <textarea
              id="productDescription"
              class="tinymce"
              data-required="true"
            ></textarea>
          </div>
          <div class="form-group">
            <label>السعر (دج)</label>
            <input
              type="number"
              id="productPrice"
              min="0"
              step="0.01"
              required
            />
          </div>
          <div class="form-group">
            <label>المخزون</label>
            <input type="number" id="productStock" min="0" required />
          </div>

          <!-- Book Specific Fields -->
          <div id="productFields" class="field-group active">
            <div class="form-group">
              <label>المؤلف</label>
              <input type="text" id="productAuthor" />
            </div>
          </div>

          <!-- Backpack Specific Fields -->
          <div id="backpackFields" class="field-group">
            <div class="form-group">
              <label>المواد</label>
              <input type="text" id="backpackMaterial" />
            </div>
            <div class="form-group">
              <label>السعة (لتر)</label>
              <input type="number" id="backpackCapacity" min="0" />
            </div>
          </div>

          <!-- Laptop Specific Fields -->
          <div id="laptopFields" class="field-group">
            <div class="form-group">
              <label>المعالج</label>
              <input type="text" id="laptopProcessor" />
            </div>
            <div class="form-group">
              <label>الذاكرة العشوائية (GB)</label>
              <input type="number" id="laptopRam" min="0" />
            </div>
            <div class="form-group">
              <label>التخزين (GB)</label>
              <input type="number" id="laptopStorage" min="0" />
            </div>
          </div>

          <div class="form-group">
            <label>صورة المنتج</label>
            <input type="file" id="productImage" accept="image/*" />
          </div>
          <button type="submit" class="action-button">حفظ</button>
        </form>
      </div>
    </div>

    <!-- Order Details Modal -->
    <div id="orderModal" class="modal">
      <div class="modal-content">
        <span class="close">&times;</span>
        <h3>تفاصيل الطلب</h3>
        <div id="orderDetails"></div>
      </div>
    </div>

    <!-- Scripts -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"
      integrity="sha512-6JR4bbn8rCKvrkdoTJd/VFyXAN4CE9XMtgykPWgKiHjou56YDJxWsi90hAeMTYxNwUnKSQu9JPc3SQUg+aGCHw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <script src="../js/utils.js"></script>
    <script src="js/selection-error-fix.js"></script>
    <script src="js/tinymce-config.js"></script>
    <script src="js/ai-magic-wand.js"></script>

    <script src="js/landing-pages.js"></script>
    <script src="js/stores-management.js"></script>
    <script src="js/admin.js"></script>
    <script src="js/product-landing.js"></script>
  </body>
</html>
