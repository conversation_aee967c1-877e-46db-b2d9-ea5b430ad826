// Gestionnaire de notifications
const notificationManager = {
    showSuccess(message) {
        const notification = {
            id: Date.now(),
            type: 'success',
            message: message
        };
        this.showNotification(notification);
    },

    showError(message) {
        const notification = {
            id: Date.now(),
            type: 'error',
            message: message
        };
        this.showNotification(notification);
    },

    showInfo(message) {
        const notification = {
            id: Date.now(),
            type: 'info',
            message: message
        };
        this.showNotification(notification);
    },
    container: null,
    checkInterval: null,
    unreadCount: 0,

    init() {
        this.container = document.getElementById('notificationsContainer');
        this.startPolling();
    },

    startPolling() {
        this.checkNotifications();
        this.checkInterval = setInterval(() => this.checkNotifications(), 30000); // Vérifier toutes les 30 secondes
    },

    async checkNotifications() {
        try {
//             const response = await fetch('../php/notifications.php?action=unread'); // Fixed: await outside async function
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

//             const text = await response.text(); // Fixed: await outside async function
            if (!text.trim()) {
                console.warn('Empty response from notifications API');
                return;
            }

            const data = JSON.parse(text);

            // Handle both old format (array) and new format (object with data property)
            let notifications = [];
            if (Array.isArray(data)) {
                notifications = data;
            } else if (data.success && Array.isArray(data.data)) {
                notifications = data.data;
            } else if (data.success && data.count === 0) {
                // No notifications
                notifications = [];
            }

            // Update counter
            this.unreadCount = notifications.length;
            this.updateBadge();

            // Show new notifications
            notifications.forEach(notification => this.showNotification(notification));

        } catch (error) {
            console.error('Erreur lors de la vérification des notifications:', error);
            // Don't show error to user for notifications polling
        }
    },

    showNotification(notification) {
        const existingNotif = document.querySelector(`[data-notification-id="${notification.id}"]`);
        if (existingNotif) return;

        const notifElement = document.createElement('div');
        notifElement.className = `notification ${notification.type} unread`;
        notifElement.dataset.notificationId = notification.id;

        notifElement.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${this.getNotificationTitle(notification.type)}</div>
                <div class="notification-message">${notification.message}</div>
            </div>
            <button class="notification-close" onclick="notificationManager.dismissNotification(${notification.id})">
                &times;
            </button>
        `;

        this.container.appendChild(notifElement);

        // Auto-dismiss success and info notifications after 5 seconds
        if (notification.type === 'success' || notification.type === 'info') {
            setTimeout(() => {
                this.dismissNotification(notification.id);
            }, 5000);
        }

        // Jouer un son de notification (with better error handling and fallback)
        this.playNotificationSound();
    },

    playNotificationSound() {
        // Use Web Audio API directly for better reliability
        try {
            this.playWebAudioNotification();
        } catch (error) {
            // Try MP3 as fallback
            try {
                const audio = new Audio('../assets/notification.mp3');
                audio.volume = 0.3;
                audio.preload = 'none';

                // Only try to play if user has interacted with the page
                if (document.hasFocus()) {
                    const playPromise = audio.play();
                    if (playPromise !== undefined) {
                        playPromise.catch(error => {
                            console.debug('All notification methods failed:', error.message);
                        });
                    }
                }
            } catch (audioError) {
                console.debug('All notification methods failed:', audioError.message);
            }
        }
    },

    playWebAudioNotification() {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            // Create a pleasant notification beep
            oscillator.frequency.value = 800; // 800 Hz
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);

            console.debug('Web Audio notification played successfully');
        } catch (error) {
            // Completely silent fallback
            console.debug('All notification sound methods failed:', error.message);
        }
    },

    getNotificationTitle(type) {
        const titles = {
            'new_order': 'طلب جديد',
            'payment_received': 'تم استلام الدفع',
            'low_stock': 'تنبيه المخزون'
        };
        return titles[type] || 'إشعار';
    },

    updateBadge() {
        const ordersLink = document.querySelector('[data-section="orders"]');
        let badge = ordersLink.querySelector('.notification-badge');

        if (this.unreadCount > 0) {
            if (!badge) {
                badge = document.createElement('span');
                badge.className = 'notification-badge';
                ordersLink.appendChild(badge);
            }
            badge.textContent = this.unreadCount;
        } else if (badge) {
            badge.remove();
        }
    },

    // Dismiss notification immediately (for local notifications)
    dismissNotification(id) {
        const notif = document.querySelector(`[data-notification-id="${id}"]`);
        if (notif) {
            notif.style.opacity = '0';
            notif.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notif.remove();
            }, 300);
        }
    },

    // Close notification and mark as read (for server notifications)
    async closeNotification(id) {
        try {
//             await fetch(`../php/notifications.php?action=mark_read&id=${id}`); // Fixed: await outside async function
            const notif = document.querySelector(`[data-notification-id="${id}"]`);
            if (notif) {
                notif.style.opacity = '0';
                notif.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    notif.remove();
                }, 300);
                this.unreadCount = Math.max(0, this.unreadCount - 1);
                this.updateBadge();
            }
        } catch (error) {
            console.error('Erreur lors de la fermeture de la notification:', error);
            // Still remove the notification from UI even if server request fails
            this.dismissNotification(id);
        }
    },

    // Clear all notifications
    clearAllNotifications() {
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notif => {
            const id = notif.dataset.notificationId;
            this.dismissNotification(id);
        });
    }
};

// Vérifier l'authentification avec gestion d'erreur améliorée
function checkAuth() {
    fetch('../php/admin.php?action=check')
        .then(response => {
            console.log('Auth check response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            console.log('Auth check raw response:', text);

            if (!text.trim()) {
                console.warn('Empty response from auth check');
                window.location.href = 'login.html';
                return;
            }

            // Try to extract JSON from response (in case there are PHP warnings)
            let data;
            try {
                // Look for JSON in the response, even if there are PHP warnings before it
                const jsonMatch = text.match(/\{.*\}$/);
                if (jsonMatch) {
                    data = JSON.parse(jsonMatch[0]);
                    if (text !== jsonMatch[0]) {
                        console.warn('⚠️ PHP warnings detected in auth response:', text.replace(jsonMatch[0], ''));
                    }
                } else {
                    data = JSON.parse(text);
                }
            } catch (parseError) {
                console.error('JSON parse error in auth check:', parseError);
                console.error('Response was:', text);
                // If it's not JSON, it might be an error page - redirect to login
                window.location.href = 'login.html';
                return;
            }

            if (!data.logged_in) {
                console.log('User not logged in, redirecting to login');
                window.location.href = 'login.html';
            } else {
                console.log('User authenticated successfully');
            }
        })
        .catch(error => {
            console.error('Auth check error:', error);
            // Only redirect to login if it's not a network error
            if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                console.error('Network error during auth check - server may be down');
                // Show a user-friendly message instead of immediate redirect
                if (confirm('خطأ في الاتصال بالخادم. هل تريد المحاولة مرة أخرى؟')) {
                    setTimeout(checkAuth, 2000); // Retry after 2 seconds
                } else {
                    window.location.href = 'login.html';
                }
            } else {
                window.location.href = 'login.html';
            }
        });
}

// Load settings data - Store settings functionality removed
async function loadSettings() {
    console.log('Settings section loaded - Store settings functionality has been removed');
    // This function is kept as a stub to prevent errors when called
}

// Store settings form submission removed - form no longer exists

// Load context menu fix
document.addEventListener('DOMContentLoaded', function() {
    // Load the context menu fix script
    const script = document.createElement('script');
    script.src = 'js/context-menu-fix.js';
    script.onload = function() {
        console.log('✅ Context menu fix loaded successfully');

        // Test the fix
        if (window.contextMenuFix && window.contextMenuFix.test) {
            window.contextMenuFix.test();
        }
    };
    script.onerror = function() {
        console.warn('⚠️ Could not load context menu fix, applying inline fix');
        applyInlineContextMenuFix();
    };
    document.head.appendChild(script);
});

// Inline fallback fix
function applyInlineContextMenuFix() {
    // Global error handler for selection-related errors
    window.addEventListener('error', function(event) {
        if (event.error && event.error.message) {
            const message = event.error.message;
            if (message.includes('rangeCount') ||
                message.includes('selection is null') ||
                message.includes('mozInputSource') ||
                message.includes('contextmenuhlpr')) {
                event.preventDefault();
                console.debug('Selection/context menu error prevented:', message);
                return true;
            }
        }
    });

    // Enhanced getSelection wrapper with comprehensive protection
    if (window.getSelection && !window._selectionProtected) {
        const originalGetSelection = window.getSelection;
        window.getSelection = function() {
            try {
                const selection = originalGetSelection.call(window);
                if (!selection) {
                    return createSafeSelection();
                }

                // Wrap the selection object to handle property access safely
                return new Proxy(selection, {
                    get: function(target, prop) {
                        try {
                            if (prop === 'rangeCount') {
                                return target.rangeCount || 0;
                            }

                            const value = target[prop];
                            if (typeof value === 'function') {
                                return function(...args) {
                                    try {
                                        return value.apply(target, args);
                                    } catch (error) {
                                        console.debug(`Selection method ${prop} error handled:`, error.message);
                                        return prop === 'getRangeAt' ? document.createRange() : undefined;
                                    }
                                };
                            }

                            return value;
                        } catch (error) {
                            console.debug(`Selection property ${prop} error handled:`, error.message);
                            return prop === 'rangeCount' ? 0 : undefined;
                        }
                    }
                });
            } catch (error) {
                console.debug('getSelection error handled:', error.message);
                return createSafeSelection();
            }
        };

        // Mark as protected to prevent multiple wrapping
        window._selectionProtected = true;
    }

    // Helper function to create safe selection object
    function createSafeSelection() {
        return {
            rangeCount: 0,
            addRange: function() {},
            removeAllRanges: function() {},
            toString: function() { return ''; },
            getRangeAt: function() { return document.createRange(); },
            collapse: function() {},
            extend: function() {},
            selectAllChildren: function() {}
        };
    }

    // Also protect document.getSelection
    if (document.getSelection && !document._selectionProtected) {
        const originalDocGetSelection = document.getSelection;
        document.getSelection = function() {
            try {
                const selection = originalDocGetSelection.call(document);
                return selection ? window.getSelection() : createSafeSelection();
            } catch (error) {
                console.debug('document.getSelection error handled:', error.message);
                return createSafeSelection();
            }
        };
        document._selectionProtected = true;
    }

    // Filter console errors from browser extensions
    if (!window._consoleProtected) {
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            const problematicPatterns = [
                /contextmenuhlpr/i,
                /mozInputSource/i,
                /can't access property.*rangeCount/i,
                /selection is null/i
            ];

            if (problematicPatterns.some(pattern => pattern.test(message))) {
                console.debug('🛡️ Extension error filtered:', message);
                return;
            }

            originalConsoleError.apply(console, args);
        };
        window._consoleProtected = true;
    }
}

// Add View More link to products table
function addViewMoreLink(row, product) {
    if (product.has_landing_page && product.landing_page_enabled) {
        const actionsCell = row.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = `/product-landing.php?slug=${product.slug}`;
        viewMoreLink.className = 'action-button view-more';
        viewMoreLink.target = '_blank';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i> عرض الصفحة';
        actionsCell.appendChild(viewMoreLink);

        const shareContainer = document.createElement('div');
        shareContainer.className = 'share-buttons';

        // Facebook Share Button
        const fbShareBtn = document.createElement('button');
        fbShareBtn.className = 'share-button facebook';
        fbShareBtn.innerHTML = '<i class="fab fa-facebook-f"></i> فيسبوك';
        fbShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const fbUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
            window.open(fbUrl, '_blank', 'width=600,height=400');
        };

        // Twitter Share Button
        const twitterShareBtn = document.createElement('button');
        twitterShareBtn.className = 'share-button twitter';
        twitterShareBtn.innerHTML = '<i class="fab fa-twitter"></i> تويتر';
        twitterShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
            window.open(twitterUrl, '_blank', 'width=600,height=400');
        };

        // WhatsApp Share Button
        const whatsappShareBtn = document.createElement('button');
        whatsappShareBtn.className = 'share-button whatsapp';
        whatsappShareBtn.innerHTML = '<i class="fab fa-whatsapp"></i> واتساب';
        whatsappShareBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(url)}`;
            window.open(whatsappUrl, '_blank');
        };

        // Copy URL Button
        const copyUrlBtn = document.createElement('button');
        copyUrlBtn.className = 'share-button copy';
        copyUrlBtn.innerHTML = '<i class="fas fa-copy"></i> نسخ الرابط';
        copyUrlBtn.onclick = (e) => {
            e.preventDefault();
            const url = `${window.location.origin}/product-landing.php?slug=${product.slug}`;
            navigator.clipboard.writeText(url).then(() => {
                alert('تم نسخ الرابط بنجاح');

                // Change button text temporarily
                const originalText = copyUrlBtn.innerHTML;
                copyUrlBtn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
                setTimeout(() => {
                    copyUrlBtn.innerHTML = originalText;
                }, 2000);
            });
        };

        shareContainer.appendChild(fbShareBtn);
        shareContainer.appendChild(twitterShareBtn);
        shareContainer.appendChild(whatsappShareBtn);
        shareContainer.appendChild(copyUrlBtn);
        actionsCell.appendChild(shareContainer);
    }
}

// Navigation dans le panneau d'administration
function initNavigation() {
    console.log('Initializing navigation...');

    // Find navigation items
    const navItems = document.querySelectorAll('.admin-nav ul li');
    console.log('Found navigation items:', navItems.length);

    if (navItems.length === 0) {
        console.error('No navigation items found!');
        return;
    }

    navItems.forEach((item, index) => {
        console.log(`Adding click listener to item ${index}:`, item);

        item.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Navigation item clicked:', this);

            // Handle logout button
            if (this.id === 'logoutBtn') {
                console.log('Logout button clicked');
                if (typeof logout === 'function') {
                    logout();
                }
                return;
            }

            const sectionId = this.getAttribute('data-section');
            if (!sectionId) return;

            console.log('Switching to section:', sectionId);

            // Remove active class from all nav items and sections
            document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                navItem.classList.remove('active');
            });
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Add active class to clicked nav item and corresponding section
            this.classList.add('active');
            const section = document.getElementById(sectionId);

            // Update page title
            updatePageTitle(sectionId);

            if (section) {
                section.classList.add('active');

                // Load section specific content
                switch(sectionId) {
                    case 'dashboard':
                        console.log('Loading dashboard...');
                        if (typeof loadDashboard === 'function') loadDashboard();
                        break;
                    case 'orders':
                        console.log('Loading orders...');
                        if (typeof loadOrders === 'function') loadOrders();
                        break;
                    case 'books':
                        console.log('Loading books...');
                        if (typeof loadProducts === 'function') {
            console.log('📦 Calling loadProducts function...');
            loadProducts();
                        } else {
                            console.error('❌ loadProducts function not found');
                        }
                        break;
                    case 'landingPages':
                        console.log('Loading landing pages...');
                        // Landing pages manager is auto-initialized, just ensure it's ready
                        if (typeof landingPagesManager !== 'undefined') {
                            console.log('Landing Pages Manager is available');
                            // Don't re-initialize, just ensure it's ready
                            if (!landingPagesManager.initialized) {
                                console.log('Landing Pages Manager not initialized yet, initializing...');
                                landingPagesManager.init();
                            } else {
                                console.log('Landing Pages Manager already initialized, refreshing data...');
                                // Always refresh the landing pages data when switching to this section
                                landingPagesManager.loadLandingPages();
                            }
                        } else {
                            console.warn('Landing Pages Manager not found');
                        }
                        break;
                    case 'reports':
                        console.log('Loading reports and analytics...');
                        loadReportsContent();
                        break;
                    case 'settings':
                        console.log('Loading settings...');
                        if (typeof loadSettings === 'function') {
                            loadSettings();
                        }
                        break;
                    // storeSettings case removed - functionality no longer exists

                    case 'categoriesManagement':
                        console.log('Loading categories management...');
                        loadCategoriesManagementContent();
                        break;
                    case 'paymentSettings':
                        console.log('Loading payment settings...');
                        loadPaymentSettingsContent();
                        break;
                    case 'generalSettings':
                        console.log('Loading general settings...');
                        loadGeneralSettingsContent();
                        break;
                    case 'storeSettings':
                        console.log('Loading store settings...');
                        loadStoreSettingsContent();
                        break;
                    case 'userManagement':
                        console.log('Loading user management...');
                        loadUserManagementContent();
                        break;
                    case 'storesManagement':
                        console.log('Loading stores management...');
                        loadStoresManagementContent();
                        break;
                    case 'rolesManagement':
                        console.log('Loading roles management...');
                        loadRolesManagementContent();
                        break;
                    case 'subscriptionsManagement':
                        console.log('Loading subscriptions management...');
                        loadSubscriptionsManagementContent();
                        break;
                    case 'securitySettings':
                        console.log('Loading security settings...');
                        loadSecuritySettingsContent();
                        break;
                    case 'systemTesting':
                        console.log('Loading system testing...');
                        loadSystemTestingContent();
                        break;
                    case 'categoriesManagement':
                        console.log('Loading categories management...');
                        loadCategoriesContent();
                        break;
                }

                // Close mobile menu if open
                closeMobileMenu();
            } else {
                console.error('Section not found:', sectionId);
            }
        });
    });

    console.log('Navigation initialization complete');
}

// Mobile menu functionality
function initMobileMenu() {
    const mobileToggle = document.getElementById('mobileMenuToggle');
    const sidebar = document.getElementById('sidebar');

    if (mobileToggle && sidebar) {
        mobileToggle.addEventListener('click', function() {
            sidebar.classList.toggle('mobile-open');

            // Update toggle icon
            const icon = this.querySelector('i');
            if (sidebar.classList.contains('mobile-open')) {
                icon.className = 'fas fa-times';
            } else {
                icon.className = 'fas fa-bars';
            }
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileToggle.contains(e.target)) {
                closeMobileMenu();
            }
        });
    }
}

function closeMobileMenu() {
    const sidebar = document.getElementById('sidebar');
    const mobileToggle = document.getElementById('mobileMenuToggle');

    if (sidebar && sidebar.classList.contains('mobile-open')) {
        sidebar.classList.remove('mobile-open');

        if (mobileToggle) {
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-bars';
            }
        }
    }
}

// Add filters for orders section
function addOrdersFilters() {
    const ordersSection = document.getElementById('orders');
    if (ordersSection && !ordersSection.querySelector('.orders-filters')) {
        const filtersHtml = `
            <div class="orders-filters">
                <div class="filter-group">
                    <label for="statusFilter">الحالة:</label>
                    <select id="statusFilter">
                        <option value="">الكل</option>
                        <option value="en_attente">قيد الانتظار</option>
                        <option value="payé">تم الدفع</option>
                        <option value="expédié">تم الشحن</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="dateFilter">التاريخ:</label>
                    <select id="dateFilter">
                        <option value="">كل التواريخ</option>
                        <option value="today">اليوم</option>
                        <option value="week">هذا الأسبوع</option>
                        <option value="month">هذا الشهر</option>
                    </select>
                </div>
            </div>
        `;
        ordersSection.insertAdjacentHTML('afterbegin', filtersHtml);

        // Add event listeners for filters
        const filters = ordersSection.querySelectorAll('select');
        filters.forEach(filter => {
            filter.addEventListener('change', () => {
                const activeFilters = {
                    status: document.getElementById('statusFilter').value,
                    date: document.getElementById('dateFilter').value
                };
                loadOrders(activeFilters);
            });
        });
    }
}

// Load dashboard data
async function loadDashboard() {
    try {
        const response = await fetch('../php/api/dashboard-stats.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'خطأ في تحميل الإحصائيات');
        }

        const data = result.data;
        if (!data || typeof data !== 'object') {
            throw new Error('بيانات غير صالحة');
        }

        console.log('Dashboard data loaded:', data);

        // Update dashboard stats with real data
        const totalBooksElement = document.getElementById('totalBooks');
        const newOrdersElement = document.getElementById('newOrders');
        const totalSalesElement = document.getElementById('totalSales');
        const totalLandingPagesElement = document.getElementById('totalLandingPages');

        // Safely access nested properties with optional chaining and nullish coalescing
        if (totalBooksElement) {
            totalBooksElement.textContent = data?.products?.total ?? 0;
        }
        if (newOrdersElement) {
            newOrdersElement.textContent = data?.orders?.pending ?? 0;
        }
        if (totalSalesElement) {
            totalSalesElement.textContent = data?.sales?.formatted_total ?? '0.00 دج';
        }
        if (totalLandingPagesElement) {
            totalLandingPagesElement.textContent = data?.landing_pages?.total ?? 0;
        }

        // Update recent orders table if it exists
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody && data.recent_orders) {
            recentOrdersBody.innerHTML = '';

            data.recent_orders.forEach(order => {
                const tr = document.createElement('tr');
                tr.style.cursor = 'pointer';
                tr.innerHTML = `
                    <td>#${order.id}</td>
                    <td>${order.nom_client || 'غير محدد'}</td>
                    <td>${order.montant_total || 0} دج</td>
                    <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                    <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                `;
                tr.onclick = () => {
                    document.querySelector('[data-section="orders"]').click();
                    setTimeout(() => showOrderDetails(order.id), 100);
                };
                recentOrdersBody.appendChild(tr);
            });
        }

        console.log('✅ Dashboard statistics updated successfully');
    } catch (error) {
        console.error('❌ Error loading dashboard stats:', error);

        // Show user-friendly error message
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('فشل في تحميل إحصائيات لوحة المعلومات. يرجى التحقق من اتصال قاعدة البيانات.');
        }

        // Set default values with error indication
        const totalBooksEl = document.getElementById('totalBooks');
        const newOrdersEl = document.getElementById('newOrders');
        const totalSalesEl = document.getElementById('totalSales');
        const totalLandingPagesEl = document.getElementById('totalLandingPages');

        if (totalBooksEl) {
            totalBooksEl.textContent = '0';
            totalBooksEl.style.color = '#dc3545';
            totalBooksEl.title = 'خطأ في تحميل البيانات';
        }
        if (newOrdersEl) {
            newOrdersEl.textContent = '0';
            newOrdersEl.style.color = '#dc3545';
            newOrdersEl.title = 'خطأ في تحميل البيانات';
        }
        if (totalSalesEl) {
            totalSalesEl.textContent = '0 دج';
            totalSalesEl.style.color = '#dc3545';
            totalSalesEl.title = 'خطأ في تحميل البيانات';
        }
        if (totalLandingPagesEl) {
            totalLandingPagesEl.textContent = '0';
            totalLandingPagesEl.style.color = '#dc3545';
            totalLandingPagesEl.title = 'خطأ في تحميل البيانات';
        }

        // Clear recent orders table
        const recentOrdersBody = document.querySelector('#recentOrdersTable tbody');
        if (recentOrdersBody) {
            recentOrdersBody.innerHTML = '<tr><td colspan="5" style="text-align: center; color: #dc3545;"><i class="fas fa-exclamation-triangle"></i> خطأ في تحميل البيانات</td></tr>';
        }

        // Add retry button to dashboard
        const dashboardSection = document.getElementById('dashboard');
        if (dashboardSection && !dashboardSection.querySelector('.retry-button')) {
            const retryButton = document.createElement('div');
            retryButton.className = 'retry-button';
            retryButton.style.cssText = 'text-align: center; margin: 20px; padding: 15px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; color: #721c24;';
            retryButton.innerHTML = `
                <p><i class="fas fa-exclamation-triangle"></i> فشل في تحميل بيانات لوحة المعلومات</p>
                <button onclick="loadDashboard(); this.parentElement.remove();" class="btn btn-primary" style="margin: 5px;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
                <a href="setup-database.php" class="btn btn-secondary" style="margin: 5px; text-decoration: none;">
                    <i class="fas fa-database"></i> إعداد قاعدة البيانات
                </a>
            `;
            dashboardSection.appendChild(retryButton);
        }
    }
}

// Charger la liste des produits
async function loadProducts() {
    try {
        console.log('📦 Loading products...');
        const response = await fetch('../php/api/products.php');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        console.log('API Response:', result);

        // Handle both array and object responses (updated for new API format)
        let data = [];
        if (Array.isArray(result)) {
            data = result;
        } else if (result.success && Array.isArray(result.data)) {
            data = result.data;
        } else if (result.success && Array.isArray(result.products)) {
            // Backward compatibility
            data = result.products;
        } else if (result.data) {
            data = result.data;
        } else if (result.products) {
            // Backward compatibility
            data = result.products;
        } else {
            console.error('Invalid API response format:', result);
            console.error('Expected: {success: true, data: [...]} or array');
            throw new Error('Invalid data format: no products found in response');
        }

        // Log data for debugging
        console.log('Products data:', data);
        console.log(`Found ${data.length} products`);

        const tbody = document.querySelector('#booksTable tbody');
        tbody.innerHTML = '';

        data.forEach((product, index) => {
                const tr = document.createElement('tr');
                const productNumber = String(index + 1).padStart(3, '0'); // Format as #001, #002, etc.
                tr.innerHTML = `
                    <td>
                        <input type="checkbox" class="product-checkbox" value="${product.id}" onchange="updateSelectedProductsCount()">
                    </td>
                    <td>
                        <span class="product-number">#${productNumber}</span>
                    </td>
                    <td><img src="${product.image_url || '../images/default-product.jpg'}" alt="${product.titre}" width="50" onerror="this.src='../images/default-product.jpg'"></td>
                    <td>${product.titre}</td>
                    <td>${getProductTypeText(product.type)}</td>
                    <td>${product.prix} دج</td>
                    <td>${product.stock}</td>
                    <td>
                        <span class="status-badge status-${product.actif ? 'active' : 'inactive'}">
                            ${product.actif ? 'نشط' : 'غير نشط'}
                        </span>
                    </td>
                    <td>
                        <button onclick="editProduct(${product.id})" class="action-button" title="تعديل المنتج">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleProductStatus(${product.id}, ${product.actif})"
                                class="action-button toggle-status-btn"
                                data-product-id="${product.id}"
                                data-current-status="${product.actif}"
                                style="background: ${product.actif ? '#e67e22' : '#27ae60'};"
                                title="${product.actif ? 'تعطيل المنتج' : 'تفعيل المنتج'}">
                            <i class="fas ${product.actif ? 'fa-pause' : 'fa-play'}"></i>
                            <span class="status-text">${product.actif ? 'مفعل' : 'معطل'}</span>
                        </button>
                        <button onclick="deleteProduct(${product.id})" class="action-button" style="background: #e74c3c;" title="حذف المنتج">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(tr);

                // Add View More link if product has landing page
                addViewMoreLink(tr, product);
            });
        } catch (error) {
            console.error('Error loading books:', error);
            notificationManager.showError('حدث خطأ أثناء تحميل المنتجات');
        }
}

// Get product type text in Arabic
function getProductTypeText(type) {
    const types = {
        'book': 'كتاب',
        'backpack': 'حقيبة ظهر',
        'laptop': 'حاسوب محمول',
        'smartphone': 'هاتف ذكي',
        'accessory': 'إكسسوار',
        'sports': 'رياضة',
        'beauty': 'تجميل',
        'game': 'لعبة',
        'clothing': 'ملابس',
        'home': 'منزل'
    };
    return types[type] || type;
}

// Enhanced function to toggle product status
async function toggleProductStatus(productId, currentStatus) {
    console.log('Toggle product status called:', { productId, currentStatus });

    const button = document.querySelector(`[data-product-id="${productId}"]`);
    const originalButtonContent = button ? button.innerHTML : '';

    // Show loading state
    if (button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    }

    try {
        const formData = new FormData();
        formData.append('productId', productId.toString());
        formData.append('active', !currentStatus ? '1' : '0');

        console.log('Sending request with data:', {
            productId: productId.toString(),
            active: !currentStatus ? '1' : '0'
        });

        const response = await fetch('../php/api/products.php?action=toggle-active', {
            method: 'POST',
            body: formData
        });

        console.log('Response status:', response.status);

        // Get response text first to handle both JSON and non-JSON responses
        const responseText = await response.text();
        console.log('Response text:', responseText);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}, response: ${responseText}`);
        }

        let data;
        try {
            data = JSON.parse(responseText);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error(`Invalid JSON response: ${responseText}`);
        }

        console.log('Parsed response data:', data);

        if (data.success) {
            // Update button immediately for better UX
            const newStatus = !currentStatus;
            if (button) {
                button.style.background = newStatus ? '#e67e22' : '#27ae60';
                button.title = newStatus ? 'تعطيل المنتج' : 'تفعيل المنتج';
                button.innerHTML = `<i class="fas ${newStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${newStatus ? 'مفعل' : 'معطل'}</span>`;
                button.setAttribute('data-current-status', newStatus);
                button.onclick = () => toggleProductStatus(productId, newStatus);
                button.disabled = false;
            }

            // Show success notification
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess(`تم ${newStatus ? 'تفعيل' : 'تعطيل'} المنتج بنجاح`);
            }

            // Refresh the product selection in landing page modal if it's open
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.refreshProductSelect) {
                landingPagesManager.refreshProductSelect();
            }

        } else {
            throw new Error(data.message || data.error || 'فشل في تحديث حالة المنتج');
        }

    } catch (error) {
        console.error('Error toggling product status:', error);

        // Reset button state
        if (button) {
            button.disabled = false;
            button.innerHTML = originalButtonContent;
            button.style.background = currentStatus ? '#e67e22' : '#27ae60';
            button.innerHTML = `<i class="fas ${currentStatus ? 'fa-pause' : 'fa-play'}"></i> <span class="status-text">${currentStatus ? 'مفعل' : 'معطل'}</span>`;
        }

        notificationManager.showError(error.message || 'حدث خطأ أثناء تحديث حالة المنتج');
    }
}

// Charger la liste des commandes
async function loadOrders(filters = {}) {
    try {
        let url = '../php/orders.php';
        if (Object.keys(filters).length > 0) {
            const params = new URLSearchParams(filters);
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const text = await response.text();
        if (!text.trim()) {
            console.warn('Empty response from orders API');
            return;
        }

        const orders = JSON.parse(text);
        const ordersTableBody = document.querySelector('#ordersTable tbody');
        ordersTableBody.innerHTML = '';

        orders.forEach(order => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>#${order.id}</td>
                <td>
                    <div class="customer-info">
                        <strong>${order.nom_client}</strong>
                        <span class="customer-email">${order.email || ''}</span>
                    </div>
                </td>
                <td>
                    <div class="order-details">
                        <strong>${order.montant_total} دج</strong>
                        <span class="items-count">${order.items ? order.items.length : 0} منتجات</span>
                    </div>
                </td>
                <td><span class="status-badge status-${order.statut}">${getStatusText(order.statut)}</span></td>
                <td>${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</td>
                <td>
                    <button onclick="showOrderDetails(${order.id})" class="action-button"><i class="fas fa-eye"></i></button>
                    <button onclick="printOrder(${order.id})" class="action-button"><i class="fas fa-print"></i></button>
                    <select onchange="updateOrderStatus(${order.id}, this.value)">
                        <option value="en_attente" ${order.statut === 'en_attente' ? 'selected' : ''}>قيد الانتظار</option>
                        <option value="payé" ${order.statut === 'payé' ? 'selected' : ''}>تم الدفع</option>
                        <option value="expédié" ${order.statut === 'expédié' ? 'selected' : ''}>تم الشحن</option>
                    </select>
                </td>
            `;
            ordersTableBody.appendChild(tr);
        });
    } catch (error) {
        console.error('Error loading orders:', error);
    }
}

// Initialize TinyMCE
function initTinyMCE() {
    console.log('Starting TinyMCE initialization...');

    // Check if TinyMCE is loaded
    if (typeof tinymce === 'undefined') {
        console.warn('TinyMCE not loaded yet, waiting...');
        setTimeout(initTinyMCE, 500);
        return;
    }

    // Add tinymce class to target textareas
    const textareas = document.querySelectorAll('#productDescription, .block-content');
    if (textareas.length === 0) {
        console.warn('No textareas found, waiting for DOM...');
        setTimeout(initTinyMCE, 500);
        return;
    }

    textareas.forEach(el => {
        el.classList.add('tinymce');
        console.log('Added tinymce class to:', el.id || 'unnamed textarea');
    });
    console.log('TinyMCE initialization - Found textareas:', textareas.length);

    // Use configuration from tinymce-config.js
    if (typeof tinymce !== 'undefined' && typeof tinymce.init === 'function') {
        tinymce.init({
            selector: 'textarea.tinymce',
            language: 'ar',
            language_url: '/admin/js/langs/ar.js',
            directionality: 'rtl',
            content_css: [
                'https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap',
                '/admin/css/tinymce-content.css'
            ],
            font_family_formats: 'Noto Sans Arabic=Noto Sans Arabic,sans-serif',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'preview', 'anchor', 'searchreplace', 'visualblocks', 'code',
                'fullscreen', 'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | preview fullscreen',
            promotion: false,
            branding: false,
            min_height: 400,
            resize: true,
            setup: function(editor) {
                editor.on('init', function() {
                    console.log('TinyMCE editor initialized:', editor.id);
                    editor.getContainer().style.direction = 'rtl';
                });

                editor.on('change', function() {
                    console.log('Content changed in editor:', editor.id);
                    console.log('Current content:', editor.getContent());
                });
            }
        });
    }
}

// Comprehensive global error handler for browser extension conflicts
window.addEventListener('error', function(e) {
    if (e.error && e.error.message) {
        const message = e.error.message;

        // Known problematic patterns from browser extensions
        const problematicPatterns = [
            /can't access property.*rangeCount.*selection is null/i,
            /contextmenuhlpr/i,
            /mozInputSource/i,
            /getSelection.*null/i,
            /selection.*rangeCount/i,
            /can't access property "rangeCount"/i,
            /selection is null/i
        ];

        if (problematicPatterns.some(pattern => pattern.test(message))) {
            console.debug('🛡️ Browser extension error suppressed:', message);
            e.preventDefault();
            e.stopPropagation();
            return true;
        }
    }

    // Legacy support for older error format
    if (e.message && (e.message.includes('rangeCount') || e.message.includes('selection is null') || e.message.includes('can\'t access property "rangeCount"'))) {
        console.debug('🛡️ DOM selection error handled globally:', e.message);
        e.preventDefault();
        return true;
    }
}, true);



// Initialize form handlers
function initFormHandlers() {
    console.log('Initializing form handlers...');

    // Check if the form exists
    let productForm = document.getElementById('productForm');
    if (!productForm) {
        console.log('Product form not found in DOM yet...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    // Wait for TinyMCE to be fully loaded
    const productDescriptionTextarea = document.getElementById('productDescription');
    if (!productDescriptionTextarea) {
        console.log('Product description textarea not found...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    if (typeof tinymce === 'undefined' || !tinymce.get('productDescription')) {
        console.log('Waiting for TinyMCE to be ready...');
        setTimeout(initFormHandlers, 500);
        return;
    }

    console.log('All requirements met, proceeding with form initialization...');

    // Handle product type selection
    const productTypeSelect = document.getElementById('productType');
    if (productTypeSelect) {
        productTypeSelect.addEventListener('change', (e) => {
            const productType = e.target.value;
            document.querySelectorAll('.field-group').forEach(group => {
                group.style.display = 'none';
                group.classList.remove('active');
            });
            const targetField = document.getElementById(`${productType}Fields`);
            if (targetField) {
                targetField.style.display = 'block';
                targetField.classList.add('active');
            }
        });
    }

    // Handle product form submission
    productForm = document.getElementById('productForm');
    if (productForm) {
        productForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData();
            const productType = document.getElementById('productType').value;

            // Common fields
            formData.append('productType', productType);
            formData.append('productTitle', document.getElementById('productTitle').value);

            // Get description content safely
            let description = '';
            const editor = tinymce.get('productDescription');
            if (editor) {
                if (!editor.initialized) {
                    console.warn('TinyMCE editor not fully initialized yet');
                    alert('يرجى الانتظار حتى يتم تحميل محرر النص بالكامل');
                    return;
                }
                console.log('TinyMCE editor found and initialized');
                description = editor.getContent();
                console.log('Description content:', description);
                if (!description.trim()) {
                    console.warn('Empty description content');
                    alert('يرجى إدخال وصف للمنتج');
                    editor.focus();
                    return;
                }
            } else {
                console.warn('TinyMCE editor not found, falling back to textarea value');
                const textarea = document.getElementById('productDescription');
                description = textarea ? textarea.value.trim() : '';
                if (!description) {
                    alert('يرجى إدخال وصف للمنتج');
                    textarea.focus();
                    return;
                }
            }
            formData.append('productDescription', description);

            formData.append('productPrice', document.getElementById('productPrice').value);
            formData.append('productStock', document.getElementById('productStock').value);

            // Category field
            const categoryField = document.getElementById('productCategory');
            if (categoryField && categoryField.value) {
                formData.append('category_id', categoryField.value);
            }

            // Type-specific fields
            switch(productType) {
                case 'book':
                    formData.append('auteur', document.getElementById('productAuthor').value);
                    break;
                case 'backpack':
                    formData.append('material', document.getElementById('backpackMaterial').value);
                    formData.append('capacity', document.getElementById('backpackCapacity').value);
                    break;
                case 'laptop':
                    formData.append('processor', document.getElementById('laptopProcessor').value);
                    formData.append('ram', document.getElementById('laptopRam').value);
                    formData.append('storage', document.getElementById('laptopStorage').value);
                    break;
            }

            const imageFile = document.getElementById('productImage').files[0];
            if (imageFile) {
                formData.append('productImage', imageFile);
            }

            // Landing page data (if productLandingManager exists)
            if (typeof productLandingManager !== 'undefined') {
                const landingData = productLandingManager.getFormData();
                formData.append('has_landing_page', landingData.hasLandingPage);
                formData.append('landing_page_enabled', landingData.landingPageEnabled);

                // Append content blocks
                landingData.contentBlocks.forEach((block, index) => {
                    formData.append(`content_blocks[${index}][title]`, block.title);
                    formData.append(`content_blocks[${index}][content]`, block.content);
                    formData.append(`content_blocks[${index}][sort_order]`, block.sortOrder);
                });

                // Append gallery images
                landingData.galleryImages.forEach((file, index) => {
                    formData.append(`gallery_images[${index}]`, file);
                });
            }

            const bookId = e.target.getAttribute('data-book-id');
            const method = 'POST';
            const url = '../php/api/products.php';
            if (bookId) {
                formData.append('productId', bookId);
            }

            // Log FormData contents
            console.log('FormData contents:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            fetch(url, {
                method: method,
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(text => {
                console.log('Raw server response:', text);
                if (!text.trim()) {
                    throw new Error('Empty response from server');
                }
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        closeModal('productModal');
                        loadProducts();
                    } else {
                        alert(data.error || 'حدث خطأ أثناء حفظ المنتج');
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.log('Response that failed to parse:', text);
                    throw new Error('Invalid JSON response from server');
                }
            })
            .catch(error => {
                console.error('Error saving product:', error);
                alert('حدث خطأ أثناء حفظ المنتج');
            });
        });
    }
}

// Fonctions utilitaires
function getStatusText(status) {
    const statusMap = {
        'en_attente': 'قيد الانتظار',
        'payé': 'تم الدفع',
        'expédié': 'تم الشحن'
    };
    return statusMap[status] || status;
}

// Load categories for product form
async function loadProductCategories() {
    try {
        console.log('Loading categories for product form...');
        const response = await fetch('/php/api/categories.php?active_only=1');
        const data = await response.json();

        if (data.success && data.categories) {
            const categorySelect = document.getElementById('productCategory');
            if (categorySelect) {
                // Clear existing options except the first one
                categorySelect.innerHTML = '<option value="">اختر الفئة...</option>';

                // Add categories as options
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.nom_ar;
                    categorySelect.appendChild(option);
                });

                console.log('✅ Categories loaded successfully:', data.categories.length);
            }
        } else {
            console.error('Failed to load categories:', data.message);
            notificationManager.showError('فشل في تحميل الفئات');
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        notificationManager.showError('خطأ في تحميل الفئات');
    }
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function logout() {
    fetch('../php/admin.php?action=logout', {
        method: 'POST'
    })
    .then(() => {
        window.location.href = 'login.html';
    });
}

// Initialize modal handlers
function initModalHandlers() {
    // Gestionnaires d'événements pour les modals
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    const addProductBtn = document.getElementById('addProductBtn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            productForm = document.getElementById('productForm');
            const modalTitle = document.getElementById('modalTitle');
            if (productForm) productForm.reset();
            if (productForm) productForm.removeAttribute('data-product-id');
            if (modalTitle) modalTitle.textContent = 'إضافة منتج جديد';
            loadProductCategories(); // Load categories when opening the form
            showModal('productModal');
        });
    }

    // Bulk deletion event listeners
    const selectAllProductsBtn = document.getElementById('selectAllProducts');
    if (selectAllProductsBtn) {
        selectAllProductsBtn.addEventListener('change', toggleSelectAllProducts);
    }

    const deleteSelectedProductsBtn = document.getElementById('deleteSelectedProductsBtn');
    if (deleteSelectedProductsBtn) {
        deleteSelectedProductsBtn.addEventListener('click', deleteSelectedProducts);
    }

    // Landing pages bulk deletion event listeners
    const selectAllLandingPagesBtn = document.getElementById('selectAllLandingPages');
    if (selectAllLandingPagesBtn) {
        selectAllLandingPagesBtn.addEventListener('change', function() {
            if (typeof window.toggleSelectAllLandingPages === 'function') {
                window.toggleSelectAllLandingPages();
            }
        });
    }

    const deleteSelectedLandingPagesBtn = document.getElementById('deleteSelectedLandingPagesBtn');
    if (deleteSelectedLandingPagesBtn) {
        deleteSelectedLandingPagesBtn.addEventListener('click', function() {
            if (typeof window.deleteSelectedLandingPages === 'function') {
                window.deleteSelectedLandingPages();
            }
        });
    }
}

// Initialize settings form handlers
function initSettingsHandlers() {
    console.log('Initializing settings handlers...');

    // Handle settings section navigation
    // Handle both old setting items and new setting cards
    const settingItems = document.querySelectorAll('.setting-item[data-section], .setting-card[data-section]');
    console.log('Found setting items:', settingItems.length);

    settingItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const sectionId = this.getAttribute('data-section');
            console.log('Settings section clicked:', sectionId);

            if (sectionId) {
                // Remove active class from all nav items and sections
                document.querySelectorAll('.admin-nav ul li').forEach(navItem => {
                    navItem.classList.remove('active');
                });
                document.querySelectorAll('.content-section').forEach(section => {
                    section.classList.remove('active');
                });

                // Add active class to settings nav item and corresponding section
                const settingsNavItem = document.querySelector('.admin-nav ul li[data-section="settings"]');
                if (settingsNavItem) {
                    settingsNavItem.classList.add('active');
                }

                const section = document.getElementById(sectionId);
                if (section) {
                    section.classList.add('active');

                    // Load section specific content
                    switch(sectionId) {
                        case 'categoriesManagement':
                            console.log('Loading categories management...');
                            loadCategoriesManagementContent();
                            break;
                        case 'paymentSettings':
                            console.log('Loading payment settings...');
                            loadPaymentSettingsContent();
                            break;
                        case 'generalSettings':
                            console.log('Loading general settings...');
                            loadGeneralSettingsContent();
                            break;
                        case 'storeSettings':
                            console.log('Loading store settings...');
                            loadStoreSettingsContent();
                            break;
                        case 'userManagement':
                            console.log('Loading user management...');
                            loadUserManagementContent();
                            break;
                        case 'rolesManagement':
                            console.log('Loading roles management...');
                            loadRolesManagementContent();
                            break;
                        case 'subscriptionsManagement':
                            console.log('Loading subscriptions management...');
                            loadSubscriptionsManagementContent();
                            break;
                        case 'securitySettings':
                            console.log('Loading security settings...');
                            loadSecuritySettingsContent();
                            break;
                        case 'systemTesting':
                            console.log('Loading system testing...');
                            loadSystemTestingContent();
                            break;
                    }
                } else {
                    console.error('Section not found:', sectionId);
                }
            }
        });
    });



    // Store settings form removed - no longer needed
}

// Add View More link for products with landing pages
function addViewMoreLink(tr, product) {
    console.log('Adding view more link for product:', {
        id: product.id,
        has_landing_page: product.has_landing_page,
        landing_url: product.landing_url
    });

    if (product.has_landing_page) {
        const td = tr.querySelector('td:last-child');
        const viewMoreLink = document.createElement('a');
        viewMoreLink.href = product.landing_url;
        viewMoreLink.target = '_blank';
        viewMoreLink.className = 'view-more-link';
        viewMoreLink.innerHTML = '<i class="fas fa-external-link-alt"></i>';
        td.appendChild(viewMoreLink);
    }
}

// Store settings functionality removed



// Missing function implementations
async function editProduct(productId) {
    console.log('🖊️ Starting edit for product ID:', productId);

    try {
        // Show loading notification
        notificationManager.showInfo('جاري تحميل بيانات المنتج...');

        const httpResponse = await fetch(`../php/api/products.php?id=${productId}`);
        console.log('📡 Response status:', httpResponse.status);

        if (!httpResponse.ok) {
            throw new Error(`HTTP error! status: ${httpResponse.status}`);
        }

        const text = await httpResponse.text();
        console.log('📄 Raw response:', text.substring(0, 200) + '...');

        if (!text.trim()) {
            throw new Error('Empty response from server');
        }

        let response;
        try {
            response = JSON.parse(text);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            throw new Error('Invalid JSON response from server');
        }

        if (!response || typeof response !== 'object') {
            throw new Error('Invalid response format');
        }

        if (!response.success) {
            throw new Error(response.message || 'Failed to load product data');
        }

        const product = response.data;
        console.log('✅ Product data loaded:', product);

        // Clear form first
        resetProductForm();

        // Populate form with product data
        const titleField = document.getElementById('productTitle');
        const descField = document.getElementById('productDescription');
        const priceField = document.getElementById('productPrice');
        const stockField = document.getElementById('productStock');
        const typeField = document.getElementById('productType');

        if (titleField) titleField.value = product.titre || '';
        if (descField) descField.value = product.description || '';
        if (priceField) priceField.value = product.prix || '';
        if (stockField) stockField.value = product.stock || '';
        if (typeField) typeField.value = product.type || 'book';

        console.log('📝 Basic fields populated');

        // Handle author field for books
        const authorField = document.getElementById('productAuthor');
        if (authorField) {
            authorField.value = product.type === 'book' ? (product.auteur || '') : '';
            const authorGroup = authorField.closest('.form-group');
            if (authorGroup) {
                authorGroup.style.display = product.type === 'book' ? 'block' : 'none';
            }
        }

        // Handle product type specific fields
        handleProductTypeChange(product.type);

        // Handle existing image
        if (product.image_url) {
            displayExistingProductImage(product.image_url);
        }

        // Set form to edit mode
        const form = document.getElementById('productForm');
        if (form) {
            form.setAttribute('data-product-id', productId);
        }

        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = 'تعديل المنتج';
        }

        // Load categories and set the product's category
        await loadProductCategories();
        const categoryField = document.getElementById('productCategory');
        if (categoryField && product.category_id) {
            categoryField.value = product.category_id;
        }

        console.log('✅ Form populated successfully');
        notificationManager.showSuccess('تم تحميل بيانات المنتج بنجاح');

        showModal('productModal');

        // Initialize AI Magic Wand for description field if available
        if (typeof window.initializeAIMagicWand === 'function') {
            setTimeout(() => {
                window.initializeAIMagicWand();
                console.log('🪄 AI Magic Wand initialized for product description');
            }, 100);
        }

    } catch (error) {
        console.error('❌ Error loading product:', error);
        notificationManager.showError('حدث خطأ أثناء تحميل بيانات المنتج: ' + error.message);
    }
}

// Helper function to reset the product form
function resetProductForm() {
    const form = document.getElementById('productForm');
    if (form) {
        form.reset();
        form.removeAttribute('data-product-id');
    }

    // Clear image preview
    const imagePreview = document.getElementById('imagePreview');
    if (imagePreview) {
        imagePreview.innerHTML = '';
    }

    // Reset modal title
    const modalTitle = document.getElementById('modalTitle');
    if (modalTitle) {
        modalTitle.textContent = 'إضافة منتج جديد';
    }
}

// Helper function to display existing product image
function displayExistingProductImage(imageUrl) {
    const imagePreview = document.getElementById('imagePreview');
    if (!imagePreview || !imageUrl) return;

    imagePreview.innerHTML = `
        <div class="preview-image existing-image" style="background-image: url(${imageUrl})">
            <button type="button" class="remove-image" onclick="removeExistingImage(this)">
                <i class="fas fa-times"></i>
            </button>
            <input type="hidden" name="existing_image" value="${imageUrl}">
        </div>
    `;
}

// Helper function to remove existing image
function removeExistingImage(button) {
    const preview = button.closest('.preview-image');
    if (preview) {
        preview.remove();
    }
}

// Helper function to handle product type changes
function handleProductTypeChange(productType) {
    // Hide all type-specific fields first
    document.querySelectorAll('.field-group').forEach(group => {
        group.style.display = 'none';
        group.classList.remove('active');
    });

    // Show relevant fields for the selected type
    const targetField = document.getElementById(`${productType}Fields`);
    if (targetField) {
        targetField.style.display = 'block';
        targetField.classList.add('active');
    }
}

async function deleteProduct(productId) {
    // Enhanced confirmation dialog with Arabic text
    const confirmMessage = `هل أنت متأكد من حذف هذا المنتج؟\n\nهذا الإجراء لا يمكن التراجع عنه وسيؤثر على:\n• جميع بيانات المنتج\n• صفحات الهبوط المرتبطة\n• الطلبات المرتبطة\n\nاضغط "موافق" للمتابعة أو "إلغاء" للتراجع.`;

    if (confirm(confirmMessage)) {
        try {
            // Show loading notification
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showInfo('جاري حذف المنتج...');
            }

            const response = await fetch(`../php/api/products.php?id=${productId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            if (result.success) {
                if (typeof notificationManager !== 'undefined') {
                    notificationManager.showSuccess('تم حذف المنتج بنجاح');
                }

                // Reload products list to reflect changes
                if (typeof loadProducts === 'function') {
                    loadProducts();
                } else if (typeof loadProductsWithPagination === 'function') {
                    loadProductsWithPagination();
                }
            } else {
                throw new Error(result.error || result.message || 'Failed to delete product');
            }
        } catch (error) {
            console.error('Error deleting product:', error);
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showError(`حدث خطأ أثناء حذف المنتج: ${error.message}`);
            } else {
                alert(`حدث خطأ أثناء حذف المنتج: ${error.message}`);
            }
        }
    }
}

// View Product Function - Display detailed product information
async function viewProduct(productId) {
    console.log('👁️ Viewing product ID:', productId);

    try {
        // Show loading notification
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showInfo('جاري تحميل بيانات المنتج...');
        }

        // Fetch product data
        const response = await fetch(`../php/api/products.php?id=${productId}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.message || 'Failed to load product data');
        }

        const product = result.data;

        // Create and show product view modal
        showProductViewModal(product);

        if (typeof notificationManager !== 'undefined') {
            notificationManager.showSuccess('تم تحميل بيانات المنتج بنجاح');
        }

    } catch (error) {
        console.error('Error viewing product:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError(`حدث خطأ أثناء تحميل بيانات المنتج: ${error.message}`);
        } else {
            alert(`حدث خطأ أثناء تحميل بيانات المنتج: ${error.message}`);
        }
    }
}

// Show Product View Modal
function showProductViewModal(product) {
    // Remove existing modal if any
    const existingModal = document.getElementById('productViewModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Create modal HTML with Arabic RTL support
    const modalHTML = `
        <div id="productViewModal" class="enhanced-modal" style="display: block;">
            <div class="modal-content" style="max-width: 800px; direction: rtl;">
                <div class="modal-header">
                    <h3><i class="fas fa-eye"></i> عرض تفاصيل المنتج</h3>
                    <button type="button" class="close-modal" onclick="closeProductViewModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="product-view-container">
                        <!-- Product Image -->
                        <div class="product-image-section" style="text-align: center; margin-bottom: 20px;">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.nom}" style="max-width: 300px; max-height: 300px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">` :
                                `<div style="width: 300px; height: 200px; background: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                                    <div style="text-align: center; color: #6c757d;">
                                        <i class="fas fa-image" style="font-size: 3rem; margin-bottom: 10px;"></i>
                                        <p>لا توجد صورة</p>
                                    </div>
                                </div>`
                            }
                        </div>

                        <!-- Product Details -->
                        <div class="product-details-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div class="detail-item">
                                <label style="font-weight: bold; color: #495057; display: block; margin-bottom: 5px;">اسم المنتج:</label>
                                <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 0;">${product.nom || 'غير محدد'}</p>
                            </div>

                            <div class="detail-item">
                                <label style="font-weight: bold; color: #495057; display: block; margin-bottom: 5px;">السعر:</label>
                                <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 0; color: #28a745; font-weight: bold;">${product.prix || '0'} دج</p>
                            </div>

                            <div class="detail-item">
                                <label style="font-weight: bold; color: #495057; display: block; margin-bottom: 5px;">الفئة:</label>
                                <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 0;">${product.categorie || 'غير محدد'}</p>
                            </div>

                            <div class="detail-item">
                                <label style="font-weight: bold; color: #495057; display: block; margin-bottom: 5px;">الحالة:</label>
                                <p style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 0;">
                                    <span class="status-badge ${product.actif ? 'status-active' : 'status-inactive'}">
                                        ${product.actif ? 'نشط' : 'غير نشط'}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- Product Description -->
                        <div class="detail-item" style="margin-top: 20px;">
                            <label style="font-weight: bold; color: #495057; display: block; margin-bottom: 5px;">الوصف:</label>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; min-height: 100px; white-space: pre-wrap;">
                                ${product.description || 'لا يوجد وصف متاح'}
                            </div>
                        </div>

                        <!-- Owner Information (for admin) -->
                        ${product.owner_name ? `
                            <div class="owner-info-section" style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3;">
                                <h4 style="margin: 0 0 10px 0; color: #1976d2;"><i class="fas fa-user"></i> معلومات المالك</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                    <div>
                                        <strong>اسم المالك:</strong> ${product.owner_name}
                                    </div>
                                    <div>
                                        <strong>معرف المالك:</strong> ${product.owner_id || 'غير محدد'}
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- Landing Page Information -->
                        ${product.landing_page_url ? `
                            <div class="landing-page-section" style="margin-top: 20px; padding: 15px; background: #f3e5f5; border-radius: 8px; border-left: 4px solid #9c27b0;">
                                <h4 style="margin: 0 0 10px 0; color: #7b1fa2;"><i class="fas fa-rocket"></i> صفحة الهبوط</h4>
                                <a href="${product.landing_page_url}" target="_blank" style="color: #7b1fa2; text-decoration: none; font-weight: 500;">
                                    <i class="fas fa-external-link-alt"></i> عرض صفحة الهبوط
                                </a>
                            </div>
                        ` : ''}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeProductViewModal()">
                        <i class="fas fa-times"></i> إغلاق
                    </button>
                    <button type="button" class="btn btn-primary" onclick="editProduct(${product.id}); closeProductViewModal();">
                        <i class="fas fa-edit"></i> تعديل المنتج
                    </button>
                    ${product.landing_page_url ? `
                        <button type="button" class="btn btn-info" onclick="window.open('${product.landing_page_url}', '_blank')">
                            <i class="fas fa-rocket"></i> عرض صفحة الهبوط
                        </button>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listener for ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeProductViewModal();
        }
    });
}

// Close Product View Modal
function closeProductViewModal() {
    const modal = document.getElementById('productViewModal');
    if (modal) {
        modal.remove();
    }
}

// Bulk product deletion functions
function updateSelectedProductsCount() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    const count = checkboxes.length;
    const countElement = document.getElementById('selectedProductsCount');
    const deleteButton = document.getElementById('deleteSelectedProductsBtn');

    if (countElement) {
        countElement.textContent = count;
    }

    if (deleteButton) {
        deleteButton.style.display = count > 0 ? 'inline-block' : 'none';
    }

    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    const allCheckboxes = document.querySelectorAll('.product-checkbox');

    if (selectAllCheckbox && allCheckboxes.length > 0) {
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }
}

function toggleSelectAllProducts() {
    const selectAllCheckbox = document.getElementById('selectAllProducts');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');

    productCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedProductsCount();
}

async function deleteSelectedProducts() {
    const selectedCheckboxes = document.querySelectorAll('.product-checkbox:checked');
    const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (selectedIds.length === 0) {
        notificationManager.showError('يرجى تحديد منتج واحد على الأقل للحذف');
        return;
    }

    const confirmMessage = `هل أنت متأكد من حذف ${selectedIds.length} منتج؟`;
    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        // Show loading state
        const deleteButton = document.getElementById('deleteSelectedProductsBtn');
        const originalText = deleteButton.innerHTML;
        deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحذف...';
        deleteButton.disabled = true;

        const response = await fetch('../php/api/products.php', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'bulk_delete',
                ids: selectedIds
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            notificationManager.showSuccess(`تم حذف ${selectedIds.length} منتج بنجاح`);
            loadProducts(); // Reload the products list

            // Update dashboard if we're on dashboard
            if (document.getElementById('dashboard').classList.contains('active')) {
                loadDashboard();
            }
        } else {
            throw new Error(result.message || 'فشل في حذف المنتجات');
        }

    } catch (error) {
        console.error('Error deleting products:', error);
        notificationManager.showError('حدث خطأ أثناء حذف المنتجات: ' + error.message);
    } finally {
        // Reset button state
        const deleteButton = document.getElementById('deleteSelectedProductsBtn');
        if (deleteButton) {
            deleteButton.innerHTML = '<i class="fas fa-trash"></i> حذف المحدد (<span id="selectedProductsCount">0</span>)';
            deleteButton.disabled = false;
        }
    }
}

function showOrderDetails(orderId) {
    fetch(`../php/orders.php?id=${orderId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            if (!text.trim()) {
                throw new Error('Empty response from server');
            }
            const order = JSON.parse(text);
            if (order) {
                const orderDetails = document.getElementById('orderDetails');
                orderDetails.innerHTML = `
                    <h4>تفاصيل الطلب #${order.id}</h4>
                    <p><strong>العميل:</strong> ${order.nom_client}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${order.email || 'غير محدد'}</p>
                    <p><strong>الهاتف:</strong> ${order.telephone || 'غير محدد'}</p>
                    <p><strong>العنوان:</strong> ${order.adresse || 'غير محدد'}</p>
                    <p><strong>المبلغ الإجمالي:</strong> ${order.montant_total} دج</p>
                    <p><strong>الحالة:</strong> ${getStatusText(order.statut)}</p>
                    <p><strong>تاريخ الطلب:</strong> ${new Date(order.date_commande).toLocaleDateString('ar-DZ')}</p>
                `;
                showModal('orderModal');
            }
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            alert('حدث خطأ أثناء تحميل تفاصيل الطلب');
        });
}

function printOrder(orderId) {
    window.open(`../php/print-order.php?id=${orderId}`, '_blank');
}

function updateOrderStatus(orderId, newStatus) {
    fetch(`../php/orders.php?id=${orderId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(text => {
        if (!text.trim()) {
            throw new Error('Empty response from server');
        }
        const result = JSON.parse(text);
        if (result.success) {
            loadOrders();
            alert('تم تحديث حالة الطلب بنجاح');
        } else {
            alert(result.error || 'حدث خطأ أثناء تحديث حالة الطلب');
        }
    })
    .catch(error => {
        console.error('Error updating order status:', error);
        alert('حدث خطأ أثناء تحديث حالة الطلب');
    });
}

// Page refresh functionality
function refreshCurrentPage() {
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (!refreshBtn) return;

    // Add loading state
    refreshBtn.classList.add('loading');
    refreshBtn.disabled = true;

    // Get current active section
    const activeSection = document.querySelector('.content-section.active');
    const currentSection = activeSection ? activeSection.id : 'dashboard';

    // Update page title
    updatePageTitle(currentSection);

    // Refresh data based on current section
    const refreshPromises = [];

    switch (currentSection) {
        case 'dashboard':
            refreshPromises.push(loadDashboard());
            break;
        case 'books':
            refreshPromises.push(loadProducts());
            break;
        case 'orders':
            refreshPromises.push(loadOrders());
            break;
        case 'landingPages':
            if (typeof landingPagesManager !== 'undefined' && landingPagesManager.loadLandingPages) {
                refreshPromises.push(landingPagesManager.loadLandingPages());
                // Also refresh the product selection dropdown
                if (landingPagesManager.loadActiveProducts) {
                    refreshPromises.push(landingPagesManager.loadActiveProducts());
                }
            }
            break;
        default:
            refreshPromises.push(loadDashboard());
    }

    // Wait for all refresh operations to complete
    Promise.all(refreshPromises)
        .then(() => {
            notificationManager.showSuccess('تم تحديث الصفحة بنجاح');
        })
        .catch((error) => {
            console.error('Error refreshing page:', error);
            notificationManager.showError('حدث خطأ أثناء تحديث الصفحة');
        })
        .finally(() => {
            // Remove loading state
            setTimeout(() => {
                refreshBtn.classList.remove('loading');
                refreshBtn.disabled = false;
            }, 500);
        });
}

// Update page title based on current section
function updatePageTitle(sectionId) {
    const pageTitle = document.getElementById('pageTitle');
    const titles = {
        'dashboard': 'لوحة المعلومات',
        'books': 'إدارة المنتجات',
        'orders': 'إدارة الطلبات',
        'landingPages': 'صفحات الهبوط',
        'reports': 'التقارير والإحصائيات',
        'settings': 'إعدادات النظام'
    };

    if (pageTitle) {
        pageTitle.textContent = titles[sectionId] || 'لوحة التحكم';
    }
}

// Initialisation
// Global error handlers
window.addEventListener('error', function(event) {
    // Handle selection-related errors
    if (event.error && event.error.message && (
        event.error.message.includes('rangeCount') ||
        event.error.message.includes('selection is null') ||
        event.error.message.includes('can\'t access property "rangeCount"')
    )) {
        event.preventDefault();
        console.debug('Selection error prevented:', event.error.message);
        return true;
    }

    // Handle JSON parsing errors
    if (event.error instanceof SyntaxError && event.error.message.includes('JSON')) {
        event.preventDefault();
        console.warn('JSON parsing error prevented:', event.error.message);
        return true;
    }
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded - Starting admin initialization...');

    // Initialize core admin functionality first
    checkAuth();
    notificationManager.init();
    initNavigation();
    initMobileMenu();
    initFormHandlers();
    initModalHandlers();
    initSettingsHandlers();
    initTinyMCE();
    loadDashboard();
    // loadStoreSettings() removed - Store Settings functionality removed

    // Landing pages manager initializes itself automatically
    // No need to initialize it here to prevent conflicts

    // Initialize refresh button
    const refreshBtn = document.getElementById('refreshPageBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', refreshCurrentPage);
    }

    console.log('Admin initialization complete');
});

// Handle page loading completion
window.addEventListener('load', function() {
    // Mark content as loaded to show page after all resources are loaded
    document.body.classList.add('content-loaded');
    // Hide loading indicator
    const loadingIndicator = document.getElementById('loading-indicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'none';
    }
    console.log('Page fully loaded');
});

// Test function for landing page modal
function testLandingPageModal() {
    console.log('🧪 Testing landing page modal...');

    // Check if landingPagesManager exists
    if (typeof landingPagesManager !== 'undefined') {
        console.log('✅ landingPagesManager found');
        console.log('Initialized:', landingPagesManager.initialized);

        if (landingPagesManager.initialized) {
            console.log('🚀 Opening modal via landingPagesManager...');
            landingPagesManager.openModal();
        } else {
            console.log('⚠️ landingPagesManager not initialized, trying to initialize...');
            landingPagesManager.init();
            setTimeout(() => {
                landingPagesManager.openModal();
            }, 500);
        }
    } else {
        console.error('❌ landingPagesManager not found');

        // Try direct modal manipulation
        const modal = document.getElementById('landingPageModal');
        if (modal) {
            console.log('🔧 Trying direct modal manipulation...');
            modal.style.display = 'block';
            modal.style.opacity = '1';
            modal.style.visibility = 'visible';
            modal.style.zIndex = '9999';
        } else {
            console.error('❌ Modal element not found');
        }
    }
}

// Safe wrapper for testLandingPageModal
function safeTestLandingPageModal() {
    console.log('🧪 Safe test landing page modal called...');

    if (typeof landingPagesManager !== 'undefined') {
        // Ensure initialization
        if (!landingPagesManager.initialized) {
            console.log('🔧 Landing pages manager not initialized, initializing now...');
            try {
                landingPagesManager.init();
            } catch (error) {
                console.error('Failed to initialize landing pages manager:', error);
                if (typeof notificationManager !== 'undefined') {
                    notificationManager.showError('فشل في تهيئة نظام إدارة صفحات الهبوط');
                }
                return;
            }
        }

        // Call the test function
        testLandingPageModal();
    } else {
        console.error('landingPagesManager not available');
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('نظام إدارة صفحات الهبوط غير متاح');
        }
    }
}

// Load Reports content
function loadReportsContent() {
    console.log('Loading reports content...');

    const container = document.getElementById('reportsContent');
    if (!container) {
        console.error('Reports content container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل التقارير والإحصائيات...</p>
            </div>
        `;

        // Load and execute the reports JavaScript only if not already loaded
        if (!window.reportsInitialized) {
            const script = document.createElement('script');
            script.src = 'js/reports.js';
            script.onload = () => {
                console.log('Reports script loaded successfully');
                window.reportsInitialized = true;
                if (typeof initializeReports === 'function') {
                        initializeReports().catch(error => {
                            console.error('Error in initializeReports:', error);
                            showReportsError('فشل في تهيئة التقارير: ' + error.message);
                        });
                    }, 100);
                } else {
                    console.error('initializeReports function not found');
                    showReportsError('خطأ في تحميل وظائف التقارير');
                }
            };
            script.onerror = () => {
                console.error('Failed to load Reports script');
                showReportsError('فشل في تحميل ملف التقارير');
            };
            document.head.appendChild(script);
        } else {
            // Script already loaded, just initialize
            if (typeof initializeReports === 'function') {
                setTimeout(() => {
                    initializeReports().catch(error => {
                        console.error('Error in initializeReports:', error);
                        showReportsError('فشل في تهيئة التقارير: ' + error.message);
                    });
                }, 100);
            } else {
                console.error('initializeReports function not found');
                showReportsError('خطأ في تحميل وظائف التقارير');
            }
        }
    } catch (error) {
        console.error('Error in loadReportsContent:', error);
        showReportsError('خطأ غير متوقع: ' + error.message);
    }

    console.log('Reports content loading initiated');
}

// Show reports error
function showReportsError(message) {
    const container = document.getElementById('reportsContent');
    if (container) {
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل التقارير</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="loadReportsContent()" style="margin-top: 15px;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load Landing Pages content with pagination and admin oversight
function loadLandingPages() {
    console.log('Loading landing pages...');

    const container = document.getElementById('landingPagesContainer');
    if (!container) {
        console.error('Landing pages container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل صفحات الهبوط...</p>
            </div>
        `;

        // Create landing pages interface with pagination
        const content = `
            <div class="landing-pages-container">
                <!-- Header with controls -->
                <div class="landing-pages-header">
                    <div class="header-left">
                        <h3><i class="fas fa-rocket"></i> صفحات الهبوط</h3>
                        <p class="subtitle">إدارة جميع صفحات الهبوط مع معلومات المالك</p>
                    </div>
                    <div class="header-right">
                        <button class="action-button primary" onclick="addNewLandingPage()">
                            <i class="fas fa-plus"></i> إضافة صفحة هبوط جديدة
                        </button>
                    </div>
                </div>

                <!-- Search and filters -->
                <div class="landing-pages-filters">
                    <div class="filter-group">
                        <input type="text" id="landingPageSearch" placeholder="البحث في صفحات الهبوط..."
                               onkeyup="searchLandingPages()" class="search-input">
                    </div>
                    <div class="filter-group">
                        <select id="landingPageStatus" onchange="filterLandingPagesByStatus()" class="filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="inactive">غير نشطة</option>
                            <option value="draft">مسودة</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <select id="landingPageOwner" onchange="filterLandingPagesByOwner()" class="filter-select">
                            <option value="">جميع المالكين</option>
                            <!-- Will be populated dynamically -->
                        </select>
                    </div>
                </div>

                <!-- Pagination controls -->
                <div class="pagination-controls">
                    <div class="pagination-info">
                        <span>عرض </span>
                        <select id="landingPagesPerPage" onchange="changeLandingPagesPerPage()" class="per-page-select">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span> صفحة هبوط لكل صفحة</span>
                    </div>
                    <div class="pagination-stats" id="landingPagesStats">
                        عرض 1-20 من 0 صفحة هبوط
                    </div>
                </div>

                <!-- Landing pages table -->
                <div class="table-container">
                    <table class="admin-table landing-pages-table">
                        <thead>
                            <tr>
                                <th>العنوان</th>
                                <th>المالك</th>
                                <th>المنتج المرتبط</th>
                                <th>الحالة</th>
                                <th>الزيارات</th>
                                <th>التحويلات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="landingPagesTableBody">
                            <!-- Landing pages will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Pagination navigation -->
                <div class="pagination-navigation">
                    <button id="prevLandingPagesBtn" onclick="previousLandingPagesPage()" class="pagination-btn" disabled>
                        <i class="fas fa-chevron-right"></i> السابق
                    </button>
                    <div class="pagination-pages" id="landingPagesPagination">
                        <!-- Page numbers will be generated here -->
                    </div>
                    <button id="nextLandingPagesBtn" onclick="nextLandingPagesPage()" class="pagination-btn" disabled>
                        التالي <i class="fas fa-chevron-left"></i>
                    </button>
                </div>
            </div>
        `;

        container.innerHTML = content;

        // Initialize landing pages data
        initializeLandingPagesData();

        console.log('Landing pages content loaded successfully');

    } catch (error) {
        console.error('Error loading landing pages content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل صفحات الهبوط</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadLandingPages()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Landing pages pagination variables
let landingPagesData = [];
let filteredLandingPagesData = [];
let currentLandingPagesPage = 1;
let landingPagesPerPage = 20;

// Initialize landing pages data
async function initializeLandingPagesData() {
    try {
        // Show loading in table
        const tbody = document.getElementById('landingPagesTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 1.5rem; color: #667eea;"></i>
                        <p style="margin-top: 10px; color: #666;">جاري تحميل صفحات الهبوط...</p>
                    </td>
                </tr>
            `;
        }

        // Load landing pages from API
        const response = await fetch('../php/api/landing-pages.php?action=list');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.message || 'Failed to load landing pages');
        }

        landingPagesData = data.data || [];
        filteredLandingPagesData = [...landingPagesData];

        // Load owners for filter
        await loadLandingPageOwners();

        // Render landing pages
        renderLandingPagesPage();

        console.log('Landing pages data loaded successfully:', landingPagesData.length, 'pages');

    } catch (error) {
        console.error('Error loading landing pages data:', error);

        // Show error in table
        const tbody = document.getElementById('landingPagesTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px; color: #dc3545;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 1.5rem; margin-bottom: 10px;"></i>
                        <p>خطأ في تحميل صفحات الهبوط: ${error.message}</p>
                        <button onclick="initializeLandingPagesData()" class="btn btn-primary" style="margin-top: 10px;">
                            <i class="fas fa-redo"></i> إعادة المحاولة
                        </button>
                    </td>
                </tr>
            `;
        }

        // Use sample data as fallback
        landingPagesData = getSampleLandingPagesData();
        filteredLandingPagesData = [...landingPagesData];
        renderLandingPagesPage();
    }
}

// Load landing page owners for filter
async function loadLandingPageOwners() {
    try {
        const response = await fetch('../php/api/users.php?action=list&role=seller');

        if (response.ok) {
            const data = await response.json();

            if (data.success) {
                const ownerSelect = document.getElementById('landingPageOwner');
                if (ownerSelect) {
                    // Clear existing options except the first one
                    ownerSelect.innerHTML = '<option value="">جميع المالكين</option>';

                    // Add owner options
                    data.data.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = user.name || user.email;
                        ownerSelect.appendChild(option);
                    });
                }
            }
        }
    } catch (error) {
        console.error('Error loading landing page owners:', error);
    }
}

// Get sample landing pages data
function getSampleLandingPagesData() {
    return [
        {
            id: 1,
            title: "صفحة هبوط لابتوب Dell Inspiron 15",
            owner_name: "أحمد محمد",
            owner_id: 1,
            product_name: "لابتوب Dell Inspiron 15 - للطلاب والمهنيين",
            product_id: 19,
            status: "active",
            visits: 1250,
            conversions: 89,
            conversion_rate: 7.12,
            created_at: "2024-01-15",
            url: "landing-page.php?id=1"
        },
        {
            id: 2,
            title: "صفحة هبوط كتاب الرياضيات المتقدمة",
            owner_name: "فاطمة علي",
            owner_id: 2,
            product_name: "كتاب الرياضيات المتقدمة - للطلاب الجامعيين",
            product_id: 20,
            status: "active",
            visits: 890,
            conversions: 67,
            conversion_rate: 7.53,
            created_at: "2024-02-10",
            url: "landing-page.php?id=2"
        },
        {
            id: 3,
            title: "صفحة هبوط Samsung Galaxy A54",
            owner_name: "محمد حسن",
            owner_id: 3,
            product_name: "Samsung Galaxy A54 5G - هاتف ذكي متطور",
            product_id: 21,
            status: "draft",
            visits: 0,
            conversions: 0,
            conversion_rate: 0,
            created_at: "2024-03-05",
            url: "landing-page.php?id=3"
        }
    ];
}

// Render landing pages page
function renderLandingPagesPage() {
    const tbody = document.getElementById('landingPagesTableBody');
    if (!tbody) return;

    const startIndex = (currentLandingPagesPage - 1) * landingPagesPerPage;
    const endIndex = startIndex + landingPagesPerPage;
    const pageData = filteredLandingPagesData.slice(startIndex, endIndex);

    if (pageData.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" style="text-align: center; padding: 40px;">
                    <i class="fas fa-rocket" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                    <h4>لا توجد صفحات هبوط</h4>
                    <p>لم يتم العثور على أي صفحات هبوط</p>
                    <button onclick="addNewLandingPage()" class="btn btn-primary" style="margin-top: 15px;">
                        <i class="fas fa-plus"></i> إضافة صفحة هبوط جديدة
                    </button>
                </td>
            </tr>
        `;
        updateLandingPagesInfo();
        return;
    }

    tbody.innerHTML = pageData.map(page => {
        const statusClass = page.status === 'active' ? 'success' :
                           page.status === 'draft' ? 'warning' : 'secondary';
        const statusText = page.status === 'active' ? 'نشطة' :
                          page.status === 'draft' ? 'مسودة' : 'غير نشطة';

        return `
            <tr>
                <td>
                    <div class="landing-page-title">
                        <strong>${page.title}</strong>
                        <div class="landing-page-url">
                            <a href="${page.url}" target="_blank" style="color: #667eea; font-size: 0.9em;">
                                <i class="fas fa-external-link-alt"></i> عرض الصفحة
                            </a>
                        </div>
                    </div>
                </td>
                <td>
                    <div class="owner-info">
                        <strong>${page.owner_name}</strong>
                        <div style="font-size: 0.9em; color: #666;">ID: ${page.owner_id}</div>
                    </div>
                </td>
                <td>
                    <div class="product-info">
                        ${page.product_name}
                        <div style="font-size: 0.9em; color: #666;">ID: ${page.product_id}</div>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <div class="stats-cell">
                        <strong>${page.visits.toLocaleString()}</strong>
                        <div style="font-size: 0.9em; color: #666;">زيارة</div>
                    </div>
                </td>
                <td>
                    <div class="stats-cell">
                        <strong>${page.conversions}</strong>
                        <div style="font-size: 0.9em; color: #666;">${page.conversion_rate.toFixed(1)}%</div>
                    </div>
                </td>
                <td>${page.created_at}</td>
                <td>
                    <div class="action-buttons">
                        <button onclick="viewLandingPage(${page.id})" class="action-button" style="background: #007bff;" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editLandingPage(${page.id})" class="action-button" style="background: #ffc107;" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleLandingPageStatus(${page.id})" class="action-button"
                                style="background: ${page.status === 'active' ? '#e67e22' : '#27ae60'};"
                                title="${page.status === 'active' ? 'تعطيل' : 'تفعيل'}">
                            <i class="fas ${page.status === 'active' ? 'fa-pause' : 'fa-play'}"></i>
                        </button>
                        <button onclick="deleteLandingPage(${page.id})" class="action-button" style="background: #e74c3c;" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    updateLandingPagesInfo();
    updateLandingPagesPagination();
}

// Update landing pages info
function updateLandingPagesInfo() {
    const statsElement = document.getElementById('landingPagesStats');
    if (!statsElement) return;

    const startIndex = (currentLandingPagesPage - 1) * landingPagesPerPage + 1;
    const endIndex = Math.min(currentLandingPagesPage * landingPagesPerPage, filteredLandingPagesData.length);
    const total = filteredLandingPagesData.length;

    if (total === 0) {
        statsElement.textContent = 'لا توجد صفحات هبوط';
    } else {
        statsElement.textContent = `عرض ${startIndex}-${endIndex} من ${total} صفحة هبوط`;
    }
}

// Update landing pages pagination
function updateLandingPagesPagination() {
    const totalPages = Math.ceil(filteredLandingPagesData.length / landingPagesPerPage);
    const paginationContainer = document.getElementById('landingPagesPagination');
    const prevBtn = document.getElementById('prevLandingPagesBtn');
    const nextBtn = document.getElementById('nextLandingPagesBtn');

    if (!paginationContainer) return;

    // Update navigation buttons
    if (prevBtn) {
        prevBtn.disabled = currentLandingPagesPage <= 1;
    }
    if (nextBtn) {
        nextBtn.disabled = currentLandingPagesPage >= totalPages;
    }

    // Generate page numbers
    let paginationHTML = '';
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentLandingPagesPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <button class="pagination-number ${i === currentLandingPagesPage ? 'active' : ''}"
                    onclick="goToLandingPagesPage(${i})">
                ${i}
            </button>
        `;
    }

    paginationContainer.innerHTML = paginationHTML;
}

// Landing pages pagination functions
function changeLandingPagesPerPage() {
    const select = document.getElementById('landingPagesPerPage');
    if (select) {
        landingPagesPerPage = parseInt(select.value);
        currentLandingPagesPage = 1;
        renderLandingPagesPage();
    }
}

function goToLandingPagesPage(page) {
    currentLandingPagesPage = page;
    renderLandingPagesPage();
}

function previousLandingPagesPage() {
    if (currentLandingPagesPage > 1) {
        currentLandingPagesPage--;
        renderLandingPagesPage();
    }
}

function nextLandingPagesPage() {
    const totalPages = Math.ceil(filteredLandingPagesData.length / landingPagesPerPage);
    if (currentLandingPagesPage < totalPages) {
        currentLandingPagesPage++;
        renderLandingPagesPage();
    }
}

// Search and filter functions
function searchLandingPages() {
    const searchInput = document.getElementById('landingPageSearch');
    if (!searchInput) return;

    const searchTerm = searchInput.value.toLowerCase().trim();

    filteredLandingPagesData = landingPagesData.filter(page => {
        return page.title.toLowerCase().includes(searchTerm) ||
               page.owner_name.toLowerCase().includes(searchTerm) ||
               page.product_name.toLowerCase().includes(searchTerm);
    });

    currentLandingPagesPage = 1;
    renderLandingPagesPage();
}

function filterLandingPagesByStatus() {
    const statusSelect = document.getElementById('landingPageStatus');
    const ownerSelect = document.getElementById('landingPageOwner');

    if (!statusSelect || !ownerSelect) return;

    const statusFilter = statusSelect.value;
    const ownerFilter = ownerSelect.value;

    filteredLandingPagesData = landingPagesData.filter(page => {
        const statusMatch = !statusFilter || page.status === statusFilter;
        const ownerMatch = !ownerFilter || page.owner_id.toString() === ownerFilter;
        return statusMatch && ownerMatch;
    });

    currentLandingPagesPage = 1;
    renderLandingPagesPage();
}

function filterLandingPagesByOwner() {
    filterLandingPagesByStatus(); // Reuse the same logic
}

// Landing page action functions
function addNewLandingPage() {
    showNotification('إضافة صفحة هبوط جديدة - قيد التطوير', 'info');
}

function viewLandingPage(id) {
    const page = landingPagesData.find(p => p.id === id);
    if (page) {
        window.open(page.url, '_blank');
    }
}

function editLandingPage(id) {
    showNotification(`تعديل صفحة الهبوط ${id} - قيد التطوير`, 'info');
}

function toggleLandingPageStatus(id) {
    const page = landingPagesData.find(p => p.id === id);
    if (page) {
        const newStatus = page.status === 'active' ? 'inactive' : 'active';
        const statusText = newStatus === 'active' ? 'تفعيل' : 'تعطيل';

        if (confirm(`هل أنت متأكد من ${statusText} صفحة الهبوط "${page.title}"؟`)) {
            page.status = newStatus;

            // Update filtered data as well
            const filteredPage = filteredLandingPagesData.find(p => p.id === id);
            if (filteredPage) {
                filteredPage.status = newStatus;
            }

            renderLandingPagesPage();
            showNotification(`تم ${statusText} صفحة الهبوط بنجاح`, 'success');
        }
    }
}

function deleteLandingPage(id) {
    const page = landingPagesData.find(p => p.id === id);
    if (page) {
        if (confirm(`هل أنت متأكد من حذف صفحة الهبوط "${page.title}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // Remove from both arrays
            landingPagesData = landingPagesData.filter(p => p.id !== id);
            filteredLandingPagesData = filteredLandingPagesData.filter(p => p.id !== id);

            // Adjust current page if necessary
            const totalPages = Math.ceil(filteredLandingPagesData.length / landingPagesPerPage);
            if (currentLandingPagesPage > totalPages && totalPages > 0) {
                currentLandingPagesPage = totalPages;
            }

            renderLandingPagesPage();
            showNotification('تم حذف صفحة الهبوط بنجاح', 'success');
        }
    }
}




// Load Categories Management content into the admin panel
async function loadCategoriesManagementContent() {
    console.log('📡 Loading categories management content...');

    const container = document.getElementById('categoriesContent');
    if (!container) {
        console.error('Categories management container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الفئات...</p>
            </div>
        `;

        // Fetch the categories management HTML content
        const response = await fetch('categories-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the categories management content from the page
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const categoriesContent = doc.querySelector('.categories-management-content') ||
                                 doc.querySelector('main') ||
                                 doc.querySelector('.main-content') ||
                                 doc.querySelector('body');

        if (categoriesContent) {
            container.innerHTML = categoriesContent.innerHTML;

            // Also extract and append the category modal if it exists
            const categoryModal = doc.querySelector('#categoryModal');
            if (categoryModal) {
                // Check if modal already exists in the main document
                if (!document.getElementById('categoryModal')) {
                    document.body.appendChild(categoryModal.cloneNode(true));
                    console.log('✅ Category modal added to document');
                } else {
                    console.log('ℹ️ Category modal already exists in document');
                }
            }

            // Load categories management script if not already loaded
            if (!window.categoriesManagementLoaded) {
                const script = document.createElement('script');
                script.src = 'js/categories-management.js';
                script.onload = () => {
                    window.categoriesManagementLoaded = true;
                    // Initialize categories management functionality
                    if (typeof window.initializeCategoriesManagement === 'function') {
                        window.initializeCategoriesManagement();
                    }
                };
                script.onerror = () => {
                    console.log('Categories management script not found, using basic functionality');
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize
                if (typeof window.initializeCategoriesManagement === 'function') {
                    window.initializeCategoriesManagement();
                }
            }

            console.log('✅ Categories management content loaded successfully');
        } else {
            throw new Error('Could not find categories management content in page');
        }

    } catch (error) {
        console.error('Error loading categories management:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                <h3 style="color: #e74c3c; margin: 15px 0;">خطأ في تحميل إدارة الفئات</h3>
                <p style="color: #666;">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button onclick="loadCategoriesManagementContent()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load Payment Settings content into the admin panel
async function loadPaymentSettingsContent() {
    console.log('📡 Loading payment settings content...');

    const container = document.getElementById('paymentSettingsContent');
    if (!container) {
        console.error('Payment settings container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الدفع...</p>
            </div>
        `;

        // Fetch the payment settings HTML content
        const response = await fetch('payment-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the payment settings content from the page
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const paymentContent = doc.querySelector('.payment-settings-content') ||
                              doc.querySelector('main') ||
                              doc.querySelector('.main-content') ||
                              doc.querySelector('body');

        if (paymentContent) {
            container.innerHTML = paymentContent.innerHTML;

            // Load payment settings script if not already loaded
            if (!window.paymentSettingsLoaded) {
                const script = document.createElement('script');
                script.src = 'js/payment-settings.js';
                script.onload = () => {
                    window.paymentSettingsLoaded = true;
                    // Initialize payment settings functionality
                    if (typeof window.initializePaymentSettings === 'function') {
                        window.initializePaymentSettings();
                    }
                };
                script.onerror = () => {
                    console.log('Payment settings script not found, using basic functionality');
                };
                document.head.appendChild(script);
            } else {
                // Script already loaded, just initialize
                if (typeof window.initializePaymentSettings === 'function') {
                    window.initializePaymentSettings();
                }
            }

            console.log('✅ Payment settings content loaded successfully');
        } else {
            throw new Error('Could not find payment settings content in page');
        }

    } catch (error) {
        console.error('Error loading payment settings:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #e74c3c;"></i>
                <h3 style="color: #e74c3c; margin: 15px 0;">خطأ في تحميل إعدادات الدفع</h3>
                <p style="color: #666;">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button onclick="loadPaymentSettingsContent()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 15px;">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load General Settings content
function loadGeneralSettingsContent() {
    console.log('📡 Loading general settings content...');

    const container = document.getElementById('generalSettingsContent');
    if (!container) {
        console.error('General settings container not found');
        return;
    }

    // Create general settings interface
    container.innerHTML = `
        <div class="settings-header">
            <h1><i class="fas fa-cog"></i> الإعدادات العامة</h1>
            <p>إدارة الإعدادات العامة للنظام</p>
        </div>

        <div class="settings-content">
            <div class="settings-section">
                <h3><i class="fas fa-globe"></i> إعدادات الموقع</h3>
                <div class="form-group">
                    <label>اسم الموقع</label>
                    <input type="text" value="متجر مصعب" class="form-control">
                </div>
                <div class="form-group">
                    <label>وصف الموقع</label>
                    <textarea class="form-control" rows="3">متجر إلكتروني متخصص في بيع الكتب والمنتجات التعليمية</textarea>
                </div>
                <div class="form-group">
                    <label>لغة الموقع الافتراضية</label>
                    <select class="form-control">
                        <option value="ar" selected>العربية</option>
                        <option value="en">English</option>
                        <option value="fr">Français</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-clock"></i> إعدادات الوقت</h3>
                <div class="form-group">
                    <label>المنطقة الزمنية</label>
                    <select class="form-control">
                        <option value="Africa/Algiers" selected>الجزائر (GMT+1)</option>
                        <option value="UTC">UTC</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>تنسيق التاريخ</label>
                    <select class="form-control">
                        <option value="d/m/Y" selected>يوم/شهر/سنة</option>
                        <option value="Y-m-d">سنة-شهر-يوم</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-envelope"></i> إعدادات البريد الإلكتروني</h3>
                <div class="form-group">
                    <label>البريد الإلكتروني للإشعارات</label>
                    <input type="email" value="<EMAIL>" class="form-control">
                </div>
                <div class="form-group">
                    <label>اسم المرسل</label>
                    <input type="text" value="متجر مصعب" class="form-control">
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveGeneralSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button class="btn btn-secondary" onclick="resetGeneralSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </div>
    `;

    console.log('✅ General settings content loaded successfully');
}

// Load User Management content
async function loadUserManagementContent() {
    console.log('Loading User Management content...');

    const container = document.getElementById('userManagementContent');
    if (!container) {
        console.error('User management container not found');
        return;
    }

    try {
        // Load the enhanced user management HTML
        const response = await fetch('user-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // Extract the user management content
        const userManagementContent = doc.querySelector('.user-management-content');

        if (userManagementContent) {
            container.innerHTML = userManagementContent.innerHTML;

            // Also extract and append the user modals if they exist
            const userModal = doc.querySelector('#userModal');
            const userDetailsModal = doc.querySelector('#userDetailsModal');

            if (userModal && !document.getElementById('userModal')) {
                document.body.appendChild(userModal.cloneNode(true));
                console.log('✅ User modal added to document');
            }

            if (userDetailsModal && !document.getElementById('userDetailsModal')) {
                document.body.appendChild(userDetailsModal.cloneNode(true));
                console.log('✅ User details modal added to document');
            }
        } else {
            container.innerHTML = htmlContent;
        }

        console.log('User Management content loaded successfully');

        // Initialize the enhanced user management system
        if (typeof initializeUserManagement === 'function') {
            console.log('Initializing enhanced user management...');
            initializeUserManagement();
        } else {
            console.log('User Management script loaded successfully');
        }

    } catch (error) {
        console.error('Error loading User Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المستخدمين</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadUserManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

// Load Security Settings content
function loadSecuritySettingsContent() {
    console.log('📡 Loading security settings content...');

    const container = document.getElementById('securitySettingsContent');
    if (!container) {
        console.error('Security settings container not found');
        return;
    }

    // Create security settings interface
    container.innerHTML = `
        <div class="settings-header">
            <h1><i class="fas fa-shield-alt"></i> إعدادات الأمان</h1>
            <p>إدارة إعدادات الأمان والحماية</p>
        </div>

        <div class="settings-content">
            <div class="settings-section">
                <h3><i class="fas fa-lock"></i> إعدادات كلمة المرور</h3>
                <div class="form-group">
                    <label>الحد الأدنى لطول كلمة المرور</label>
                    <input type="number" value="8" min="6" max="20" class="form-control">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> يجب أن تحتوي على أحرف كبيرة وصغيرة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> يجب أن تحتوي على أرقام
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> يجب أن تحتوي على رموز خاصة
                    </label>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-user-shield"></i> إعدادات تسجيل الدخول</h3>
                <div class="form-group">
                    <label>عدد محاولات تسجيل الدخول المسموحة</label>
                    <input type="number" value="5" min="3" max="10" class="form-control">
                </div>
                <div class="form-group">
                    <label>مدة الحظر بعد المحاولات الفاشلة (بالدقائق)</label>
                    <input type="number" value="15" min="5" max="60" class="form-control">
                </div>
                <div class="form-group">
                    <label>مدة انتهاء الجلسة (بالدقائق)</label>
                    <input type="number" value="30" min="15" max="120" class="form-control">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تفعيل المصادقة الثنائية
                    </label>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-database"></i> أمان البيانات</h3>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تشفير البيانات الحساسة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" checked> تسجيل العمليات الحساسة
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox"> إنشاء نسخ احتياطية تلقائية
                    </label>
                </div>
                <div class="form-group">
                    <label>تكرار النسخ الاحتياطية</label>
                    <select class="form-control">
                        <option value="daily">يومياً</option>
                        <option value="weekly" selected>أسبوعياً</option>
                        <option value="monthly">شهرياً</option>
                    </select>
                </div>
            </div>

            <div class="settings-section">
                <h3><i class="fas fa-history"></i> سجل الأنشطة</h3>
                <div class="activity-log">
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:30:25</span>
                        <span class="log-action">تسجيل دخول ناجح</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:25:10</span>
                        <span class="log-action">تعديل إعدادات المنتج</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                    <div class="log-entry">
                        <span class="log-time">2024-07-13 14:20:05</span>
                        <span class="log-action">إضافة منتج جديد</span>
                        <span class="log-user"><EMAIL></span>
                        <span class="log-ip">*************</span>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveSecuritySettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button class="btn btn-warning" onclick="runSecurityScan()">
                    <i class="fas fa-search"></i> فحص أمني شامل
                </button>
                <button class="btn btn-danger" onclick="clearSecurityLogs()">
                    <i class="fas fa-trash"></i> مسح سجل الأنشطة
                </button>
            </div>
        </div>
    `;

    console.log('✅ Security settings content loaded successfully');
}

/**
 * Load General Settings content dynamically
 */
async function loadGeneralSettingsContent() {
    const container = document.getElementById('generalSettingsContent');
    if (!container) return;

    try {
        console.log('Loading General Settings content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل الإعدادات العامة...</p>
            </div>
        `;

        // Load the general settings HTML content
        const response = await fetch('general-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the general settings content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const generalSettingsContent = doc.querySelector('.general-settings-content') ||
                                      doc.querySelector('.main-content') ||
                                      doc.querySelector('body');

        if (generalSettingsContent) {
            container.innerHTML = generalSettingsContent.innerHTML;
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the general settings JavaScript
        if (!document.querySelector('script[src="js/general-settings.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/general-settings.js';
            script.onload = () => {
                console.log('General Settings script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load General Settings script');
            };
            document.head.appendChild(script);
        }

        console.log('General Settings content loaded successfully');

    } catch (error) {
        console.error('Error loading General Settings content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل الإعدادات العامة</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadGeneralSettingsContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Create role modal
 */
function createRoleModal() {
    // Check if modal already exists
    let modal = document.getElementById('roleModal');
    if (modal) return;

    // Create modal HTML
    const modalHTML = `
        <div id="roleModal" class="enhanced-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalRoleTitle">إضافة دور جديد</h3>
                    <button type="button" class="modal-close" onclick="closeRoleModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="roleForm">
                        <input type="hidden" id="roleId">

                        <div class="form-row">
                            <div class="form-group">
                                <label for="roleName" class="enhanced-label">
                                    <i class="fas fa-tag"></i>
                                    اسم الدور (بالإنجليزية)
                                </label>
                                <input type="text" id="roleName" class="enhanced-input" required
                                       placeholder="admin, store_owner, customer">
                            </div>
                            <div class="form-group">
                                <label for="roleLevel" class="enhanced-label">
                                    <i class="fas fa-layer-group"></i>
                                    مستوى الدور
                                </label>
                                <input type="number" id="roleLevel" class="enhanced-input" required
                                       min="1" max="100" placeholder="1-100">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="roleDisplayNameAr" class="enhanced-label">
                                    <i class="fas fa-language"></i>
                                    الاسم بالعربية
                                </label>
                                <input type="text" id="roleDisplayNameAr" class="enhanced-input" required
                                       placeholder="مدير، صاحب متجر، عميل">
                            </div>
                            <div class="form-group">
                                <label for="roleDisplayNameEn" class="enhanced-label">
                                    <i class="fas fa-language"></i>
                                    الاسم بالإنجليزية
                                </label>
                                <input type="text" id="roleDisplayNameEn" class="enhanced-input" required
                                       placeholder="Admin, Store Owner, Customer">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="roleDescription" class="enhanced-label">
                                <i class="fas fa-align-right"></i>
                                الوصف
                            </label>
                            <textarea id="roleDescription" class="enhanced-input" rows="3"
                                      placeholder="وصف مختصر للدور وصلاحياته"></textarea>
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="fas fa-key"></i>
                                الصلاحيات والأذونات
                            </label>
                            <div class="permissions-container">
                                <div class="permissions-header">
                                    <div class="permission-actions">
                                        <button type="button" class="btn-permission-action" onclick="selectAllPermissions()">
                                            <i class="fas fa-check-double"></i>
                                            تحديد الكل
                                        </button>
                                        <button type="button" class="btn-permission-action" onclick="clearAllPermissions()">
                                            <i class="fas fa-times"></i>
                                            إلغاء الكل
                                        </button>
                                    </div>
                                </div>

                                <div class="permissions-sections">
                                    <!-- إدارة النظام -->
                                    <div class="permission-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-cogs"></i>
                                            إدارة النظام
                                        </h4>
                                        <div class="permissions-grid">
                                            <label class="permission-item admin-permission">
                                                <input type="checkbox" name="permissions" value="admin_access">
                                                <span class="permission-icon"><i class="fas fa-user-shield"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">الوصول للوحة الإدارة</span>
                                                    <span class="permission-desc">الوصول الكامل للوحة التحكم</span>
                                                </div>
                                            </label>
                                            <label class="permission-item admin-permission">
                                                <input type="checkbox" name="permissions" value="users">
                                                <span class="permission-icon"><i class="fas fa-users"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة المستخدمين</span>
                                                    <span class="permission-desc">إضافة وتعديل وحذف المستخدمين</span>
                                                </div>
                                            </label>
                                            <label class="permission-item admin-permission">
                                                <input type="checkbox" name="permissions" value="roles">
                                                <span class="permission-icon"><i class="fas fa-user-tag"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة الأدوار</span>
                                                    <span class="permission-desc">إنشاء وتعديل أدوار المستخدمين</span>
                                                </div>
                                            </label>
                                            <label class="permission-item admin-permission">
                                                <input type="checkbox" name="permissions" value="settings">
                                                <span class="permission-icon"><i class="fas fa-cog"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة الإعدادات</span>
                                                    <span class="permission-desc">تكوين إعدادات النظام</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- إدارة المحتوى -->
                                    <div class="permission-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-box"></i>
                                            إدارة المحتوى
                                        </h4>
                                        <div class="permissions-grid">
                                            <label class="permission-item content-permission">
                                                <input type="checkbox" name="permissions" value="products">
                                                <span class="permission-icon"><i class="fas fa-shopping-bag"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة المنتجات</span>
                                                    <span class="permission-desc">إضافة وتعديل المنتجات</span>
                                                </div>
                                            </label>
                                            <label class="permission-item content-permission">
                                                <input type="checkbox" name="permissions" value="categories">
                                                <span class="permission-icon"><i class="fas fa-tags"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة الفئات</span>
                                                    <span class="permission-desc">تنظيم فئات المنتجات</span>
                                                </div>
                                            </label>
                                            <label class="permission-item content-permission">
                                                <input type="checkbox" name="permissions" value="landing_pages">
                                                <span class="permission-icon"><i class="fas fa-file-alt"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">صفحات الهبوط</span>
                                                    <span class="permission-desc">إنشاء وإدارة صفحات الهبوط</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- إدارة المبيعات -->
                                    <div class="permission-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-chart-line"></i>
                                            إدارة المبيعات
                                        </h4>
                                        <div class="permissions-grid">
                                            <label class="permission-item sales-permission">
                                                <input type="checkbox" name="permissions" value="orders">
                                                <span class="permission-icon"><i class="fas fa-shopping-cart"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">إدارة الطلبات</span>
                                                    <span class="permission-desc">معالجة ومتابعة الطلبات</span>
                                                </div>
                                            </label>
                                            <label class="permission-item sales-permission">
                                                <input type="checkbox" name="permissions" value="reports">
                                                <span class="permission-icon"><i class="fas fa-chart-bar"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">التقارير والإحصائيات</span>
                                                    <span class="permission-desc">عرض تقارير المبيعات</span>
                                                </div>
                                            </label>
                                            <label class="permission-item sales-permission">
                                                <input type="checkbox" name="permissions" value="analytics">
                                                <span class="permission-icon"><i class="fas fa-analytics"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">التحليلات المتقدمة</span>
                                                    <span class="permission-desc">تحليل بيانات المبيعات</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- الصلاحيات الشخصية -->
                                    <div class="permission-section">
                                        <h4 class="section-title">
                                            <i class="fas fa-user"></i>
                                            الصلاحيات الشخصية
                                        </h4>
                                        <div class="permissions-grid">
                                            <label class="permission-item personal-permission">
                                                <input type="checkbox" name="permissions" value="own_store">
                                                <span class="permission-icon"><i class="fas fa-store"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">المتجر الشخصي</span>
                                                    <span class="permission-desc">إدارة المتجر الخاص</span>
                                                </div>
                                            </label>
                                            <label class="permission-item personal-permission">
                                                <input type="checkbox" name="permissions" value="own_products">
                                                <span class="permission-icon"><i class="fas fa-box-open"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">المنتجات الشخصية</span>
                                                    <span class="permission-desc">إدارة المنتجات الخاصة فقط</span>
                                                </div>
                                            </label>
                                            <label class="permission-item personal-permission">
                                                <input type="checkbox" name="permissions" value="browse">
                                                <span class="permission-icon"><i class="fas fa-eye"></i></span>
                                                <div class="permission-details">
                                                    <span class="permission-name">تصفح المنتجات</span>
                                                    <span class="permission-desc">عرض وتصفح المنتجات فقط</span>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="roleStatus" checked>
                                <span>الدور نشط</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeRoleModal()">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveRole()">حفظ</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listeners for permission checkboxes
    setTimeout(() => {
        const checkboxes = document.querySelectorAll('#roleModal input[name="permissions"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updatePermissionSectionStates);
        });
    }, 100);
}

/**
 * Close role modal
 */
function closeRoleModal() {
    const modal = document.getElementById('roleModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Select all permissions
 */
function selectAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updatePermissionSectionStates();
}

/**
 * Clear all permissions
 */
function clearAllPermissions() {
    const checkboxes = document.querySelectorAll('input[name="permissions"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updatePermissionSectionStates();
}

/**
 * Update permission section visual states
 */
function updatePermissionSectionStates() {
    const sections = document.querySelectorAll('.permission-section');
    sections.forEach(section => {
        const checkboxes = section.querySelectorAll('input[name="permissions"]');
        const checkedCount = section.querySelectorAll('input[name="permissions"]:checked').length;
        const totalCount = checkboxes.length;

        const sectionTitle = section.querySelector('.section-title');
        if (sectionTitle) {
            // Remove existing badges
            const existingBadge = sectionTitle.querySelector('.section-badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // Add new badge
            if (checkedCount > 0) {
                const badge = document.createElement('span');
                badge.className = 'section-badge';
                badge.textContent = `${checkedCount}/${totalCount}`;
                if (checkedCount === totalCount) {
                    badge.classList.add('complete');
                }
                sectionTitle.appendChild(badge);
            }
        }
    });
}

/**
 * Validate role form
 */
function validateRoleForm() {
    const errors = [];

    // Required fields validation
    const roleName = document.getElementById('roleName').value.trim();
    const roleDisplayNameAr = document.getElementById('roleDisplayNameAr').value.trim();
    const roleDisplayNameEn = document.getElementById('roleDisplayNameEn').value.trim();
    const roleLevel = document.getElementById('roleLevel').value;

    if (!roleName) {
        errors.push('اسم الدور مطلوب');
    } else if (!/^[a-z_]+$/.test(roleName)) {
        errors.push('اسم الدور يجب أن يحتوي على أحرف إنجليزية صغيرة وشرطة سفلية فقط');
    }

    if (!roleDisplayNameAr) {
        errors.push('الاسم بالعربية مطلوب');
    }

    if (!roleDisplayNameEn) {
        errors.push('الاسم بالإنجليزية مطلوب');
    }

    if (!roleLevel || roleLevel < 1 || roleLevel > 100) {
        errors.push('مستوى الدور يجب أن يكون بين 1 و 100');
    }

    // Check if at least one permission is selected
    const selectedPermissions = document.querySelectorAll('input[name="permissions"]:checked');
    if (selectedPermissions.length === 0) {
        errors.push('يجب اختيار صلاحية واحدة على الأقل');
    }

    return errors;
}

/**
 * Save role
 */
async function saveRole() {
    // Validate form
    const validationErrors = validateRoleForm();
    if (validationErrors.length > 0) {
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في البيانات:\n' + validationErrors.join('\n'));
        } else {
            alert('خطأ في البيانات:\n' + validationErrors.join('\n'));
        }
        return;
    }

    // Collect form data
    const roleData = {
        name: document.getElementById('roleName').value.trim(),
        display_name_ar: document.getElementById('roleDisplayNameAr').value.trim(),
        display_name_en: document.getElementById('roleDisplayNameEn').value.trim(),
        description: document.getElementById('roleDescription').value.trim(),
        level: parseInt(document.getElementById('roleLevel').value),
        is_active: document.getElementById('roleStatus').checked ? 1 : 0,
        permissions: []
    };

    // Collect permissions
    document.querySelectorAll('input[name="permissions"]:checked').forEach(checkbox => {
        roleData.permissions.push(checkbox.value);
    });

    const roleId = document.getElementById('roleId').value;
    const isEdit = roleId && roleId !== '';

    if (isEdit) {
        roleData.id = parseInt(roleId);
    }

    try {
        const url = isEdit ? '../php/api/roles.php?action=update' : '../php/api/roles.php?action=create';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(roleData)
        });

        const result = await response.json();

        if (result.success) {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess(isEdit ? 'تم تحديث الدور بنجاح' : 'تم إضافة الدور بنجاح');
            } else {
                alert(isEdit ? 'تم تحديث الدور بنجاح' : 'تم إضافة الدور بنجاح');
            }

            closeRoleModal();
            loadRolesFromAPI(); // Reload roles list
        } else {
            throw new Error(result.message || 'فشل في حفظ الدور');
        }
    } catch (error) {
        console.error('Error saving role:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في حفظ الدور: ' + error.message);
        } else {
            alert('خطأ في حفظ الدور: ' + error.message);
        }
    }
}

/**
 * Create subscription modal
 */
function createSubscriptionModal() {
    // Check if modal already exists
    let modal = document.getElementById('subscriptionModal');
    if (modal) return;

    // Create modal HTML
    const modalHTML = `
        <div id="subscriptionModal" class="enhanced-modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="modalSubscriptionTitle">إضافة خطة اشتراك جديدة</h3>
                    <button type="button" class="modal-close" onclick="closeSubscriptionModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="subscriptionForm">
                        <input type="hidden" id="subscriptionId">

                        <div class="form-row">
                            <div class="form-group">
                                <label for="subscriptionName" class="enhanced-label">
                                    <i class="fas fa-tag"></i>
                                    اسم الخطة (بالإنجليزية)
                                </label>
                                <input type="text" id="subscriptionName" class="enhanced-input" required
                                       placeholder="free, basic, premium">
                            </div>
                            <div class="form-group">
                                <label for="subscriptionCurrency" class="enhanced-label">
                                    <i class="fas fa-coins"></i>
                                    العملة
                                </label>
                                <select id="subscriptionCurrency" class="enhanced-select">
                                    <option value="DZD">دينار جزائري (DZD)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                    <option value="EUR">يورو (EUR)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="subscriptionDisplayNameAr" class="enhanced-label">
                                    <i class="fas fa-language"></i>
                                    الاسم بالعربية
                                </label>
                                <input type="text" id="subscriptionDisplayNameAr" class="enhanced-input" required
                                       placeholder="مجاني، أساسي، مميز">
                            </div>
                            <div class="form-group">
                                <label for="subscriptionDisplayNameEn" class="enhanced-label">
                                    <i class="fas fa-language"></i>
                                    الاسم بالإنجليزية
                                </label>
                                <input type="text" id="subscriptionDisplayNameEn" class="enhanced-input" required
                                       placeholder="Free, Basic, Premium">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="subscriptionDescriptionAr" class="enhanced-label">
                                    <i class="fas fa-align-right"></i>
                                    الوصف بالعربية
                                </label>
                                <textarea id="subscriptionDescriptionAr" class="enhanced-input" rows="2"
                                          placeholder="وصف الخطة بالعربية"></textarea>
                            </div>
                            <div class="form-group">
                                <label for="subscriptionDescriptionEn" class="enhanced-label">
                                    <i class="fas fa-align-right"></i>
                                    الوصف بالإنجليزية
                                </label>
                                <textarea id="subscriptionDescriptionEn" class="enhanced-input" rows="2"
                                          placeholder="Plan description in English"></textarea>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="subscriptionPrice" class="enhanced-label">
                                    <i class="fas fa-money-bill"></i>
                                    السعر
                                </label>
                                <input type="number" id="subscriptionPrice" class="enhanced-input" required
                                       min="0" step="0.01" placeholder="0.00">
                            </div>
                            <div class="form-group">
                                <label for="subscriptionDuration" class="enhanced-label">
                                    <i class="fas fa-calendar"></i>
                                    المدة (بالأيام)
                                </label>
                                <input type="number" id="subscriptionDuration" class="enhanced-input" required
                                       min="1" value="30" placeholder="30">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="enhanced-label">
                                <i class="fas fa-cogs"></i>
                                حدود الخطة
                            </label>
                            <div class="limits-grid">
                                <div class="limit-item">
                                    <label for="maxProducts">عدد المنتجات</label>
                                    <input type="number" id="maxProducts" class="enhanced-input"
                                           min="-1" value="5" placeholder="5 (-1 = غير محدود)">
                                </div>
                                <div class="limit-item">
                                    <label for="maxLandingPages">صفحات الهبوط</label>
                                    <input type="number" id="maxLandingPages" class="enhanced-input"
                                           min="-1" value="2" placeholder="2 (-1 = غير محدود)">
                                </div>
                                <div class="limit-item">
                                    <label for="maxStorageMb">التخزين (ميجابايت)</label>
                                    <input type="number" id="maxStorageMb" class="enhanced-input"
                                           min="-1" value="100" placeholder="100 (-1 = غير محدود)">
                                </div>
                                <div class="limit-item">
                                    <label for="maxTemplates">القوالب</label>
                                    <input type="number" id="maxTemplates" class="enhanced-input"
                                           min="-1" value="5" placeholder="5 (-1 = غير محدود)">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="subscriptionStatus" checked>
                                <span>الخطة نشطة</span>
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeSubscriptionModal()">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveSubscription()">حفظ</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

/**
 * Close subscription modal
 */
function closeSubscriptionModal() {
    const modal = document.getElementById('subscriptionModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Validate subscription form
 */
function validateSubscriptionForm() {
    const errors = [];

    // Required fields validation
    const subscriptionName = document.getElementById('subscriptionName').value.trim();
    const displayNameAr = document.getElementById('subscriptionDisplayNameAr').value.trim();
    const displayNameEn = document.getElementById('subscriptionDisplayNameEn').value.trim();
    const price = document.getElementById('subscriptionPrice').value;
    const duration = document.getElementById('subscriptionDuration').value;
    const maxProducts = document.getElementById('maxProducts').value;
    const maxLandingPages = document.getElementById('maxLandingPages').value;
    const maxStorageMb = document.getElementById('maxStorageMb').value;
    const maxTemplates = document.getElementById('maxTemplates').value;

    if (!subscriptionName) {
        errors.push('اسم الخطة مطلوب');
    } else if (!/^[a-z_]+$/.test(subscriptionName)) {
        errors.push('اسم الخطة يجب أن يحتوي على أحرف إنجليزية صغيرة وشرطة سفلية فقط');
    }

    if (!displayNameAr) {
        errors.push('الاسم بالعربية مطلوب');
    }

    if (!displayNameEn) {
        errors.push('الاسم بالإنجليزية مطلوب');
    }

    if (!price || price < 0) {
        errors.push('السعر يجب أن يكون رقم موجب أو صفر');
    }

    if (!duration || duration < 1) {
        errors.push('المدة يجب أن تكون يوم واحد على الأقل');
    }

    if (maxProducts !== '' && maxProducts < -1) {
        errors.push('عدد المنتجات يجب أن يكون -1 (غير محدود) أو رقم موجب');
    }

    if (maxLandingPages !== '' && maxLandingPages < -1) {
        errors.push('عدد صفحات الهبوط يجب أن يكون -1 (غير محدود) أو رقم موجب');
    }

    if (maxStorageMb !== '' && maxStorageMb < -1) {
        errors.push('مساحة التخزين يجب أن تكون -1 (غير محدود) أو رقم موجب');
    }

    if (maxTemplates !== '' && maxTemplates < -1) {
        errors.push('عدد القوالب يجب أن يكون -1 (غير محدود) أو رقم موجب');
    }

    return errors;
}

/**
 * Save subscription
 */
async function saveSubscription() {
    // Validate form
    const validationErrors = validateSubscriptionForm();
    if (validationErrors.length > 0) {
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في البيانات:\n' + validationErrors.join('\n'));
        } else {
            alert('خطأ في البيانات:\n' + validationErrors.join('\n'));
        }
        return;
    }

    // Helper function to safely get element value
    const getElementValue = (id, defaultValue = '') => {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`Element with id '${id}' not found`);
            return defaultValue;
        }
        return element.type === 'checkbox' ? element.checked : element.value;
    };

    // Collect form data with null checks
    const subscriptionData = {
        name: (getElementValue('subscriptionName') || '').trim(),
        display_name_ar: (getElementValue('subscriptionDisplayNameAr') || '').trim(),
        display_name_en: (getElementValue('subscriptionDisplayNameEn') || '').trim(),
        description_ar: (getElementValue('subscriptionDescriptionAr') || '').trim(),
        description_en: (getElementValue('subscriptionDescriptionEn') || '').trim(),
        price: parseFloat(getElementValue('subscriptionPrice', '0')) || 0,
        currency: getElementValue('subscriptionCurrency', 'DZD'),
        duration_days: parseInt(getElementValue('subscriptionDuration', '30')) || 30,
        max_products: parseInt(getElementValue('maxProducts', '0')) || 0,
        max_landing_pages: parseInt(getElementValue('maxLandingPages', '0')) || 0,
        max_storage_mb: parseInt(getElementValue('maxStorageMb', '0')) || 0,
        max_templates: parseInt(getElementValue('maxTemplates', '0')) || 0,
        is_active: getElementValue('subscriptionStatus') ? 1 : 0
    };

    const subscriptionId = getElementValue('subscriptionId');
    const isEdit = subscriptionId && subscriptionId !== '';

    if (isEdit) {
        subscriptionData.id = parseInt(subscriptionId);
    }

    try {
        const url = isEdit ? '../php/api/subscriptions.php?action=update_plan' : '../php/api/subscriptions.php?action=create_plan';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(subscriptionData)
        });

        const result = await response.json();

        if (result.success) {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess(isEdit ? 'تم تحديث خطة الاشتراك بنجاح' : 'تم إضافة خطة الاشتراك بنجاح');
            } else {
                alert(isEdit ? 'تم تحديث خطة الاشتراك بنجاح' : 'تم إضافة خطة الاشتراك بنجاح');
            }

            closeSubscriptionModal();
            loadSubscriptionsFromAPI(); // Reload subscriptions list
        } else {
            throw new Error(result.message || 'فشل في حفظ خطة الاشتراك');
        }
    } catch (error) {
        console.error('Error saving subscription:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في حفظ خطة الاشتراك: ' + error.message);
        } else {
            alert('خطأ في حفظ خطة الاشتراك: ' + error.message);
        }
    }
}

/**
 * Load Store Settings content dynamically
 */
async function loadStoreSettingsContent() {
    const container = document.getElementById('storeSettingsContent');
    if (!container) return;

    try {
        console.log('Loading Store Settings content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات المتجر...</p>
            </div>
        `;

        // Create store settings interface
        container.innerHTML = `
            <div class="store-settings-content">
                <div class="store-settings-header">
                    <h1><i class="fas fa-store"></i> إعدادات المتجر</h1>
                    <p>إدارة إعدادات المتجر والمنتجات</p>
                </div>

                <div class="settings-grid">
                    <!-- Store Information -->
                    <div class="setting-section">
                        <h3><i class="fas fa-info-circle"></i> معلومات المتجر</h3>
                        <div class="form-group">
                            <label>اسم المتجر</label>
                            <input type="text" id="storeName" placeholder="اسم المتجر" value="متجر مصعب">
                        </div>
                        <div class="form-group">
                            <label>وصف المتجر</label>
                            <textarea id="storeDescription" placeholder="وصف المتجر" rows="3">متجر إلكتروني متخصص في بيع المنتجات عالية الجودة</textarea>
                        </div>
                        <div class="form-group">
                            <label>عنوان المتجر</label>
                            <input type="text" id="storeAddress" placeholder="العنوان" value="الجزائر العاصمة">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" id="storePhone" placeholder="+213 XXX XXX XXX" value="+213 555 123 456">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" id="storeEmail" placeholder="<EMAIL>" value="<EMAIL>">
                        </div>
                    </div>

                    <!-- Product Settings -->
                    <div class="setting-section">
                        <h3><i class="fas fa-box"></i> إعدادات المنتجات</h3>
                        <div class="form-group">
                            <label>عدد المنتجات لكل صفحة</label>
                            <input type="number" id="productsPerPage" value="12" min="1" max="50">
                        </div>
                        <div class="form-group">
                            <label>ترتيب المنتجات الافتراضي</label>
                            <select id="defaultProductSort">
                                <option value="newest">الأحدث</option>
                                <option value="price_low">السعر: من الأقل للأعلى</option>
                                <option value="price_high">السعر: من الأعلى للأقل</option>
                                <option value="name">الاسم</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="showOutOfStock" checked>
                                <span>عرض المنتجات غير المتوفرة</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="enableReviews" checked>
                                <span>تفعيل تقييمات المنتجات</span>
                            </label>
                        </div>
                    </div>

                    <!-- Currency Settings -->
                    <div class="setting-section">
                        <h3><i class="fas fa-coins"></i> إعدادات العملة</h3>
                        <div class="form-group">
                            <label>العملة الافتراضية</label>
                            <select id="defaultCurrency">
                                <option value="DZD" selected>دينار جزائري (DZD)</option>
                                <option value="USD">دولار أمريكي (USD)</option>
                                <option value="EUR">يورو (EUR)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>رمز العملة</label>
                            <input type="text" id="currencySymbol" value="دج" maxlength="5">
                        </div>
                        <div class="form-group">
                            <label>موضع رمز العملة</label>
                            <select id="currencyPosition">
                                <option value="after" selected>بعد المبلغ</option>
                                <option value="before">قبل المبلغ</option>
                            </select>
                        </div>
                    </div>

                    <!-- Inventory Settings -->
                    <div class="setting-section">
                        <h3><i class="fas fa-warehouse"></i> إعدادات المخزون</h3>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="trackInventory" checked>
                                <span>تتبع المخزون</span>
                            </label>
                        </div>
                        <div class="form-group">
                            <label>حد التنبيه للمخزون المنخفض</label>
                            <input type="number" id="lowStockThreshold" value="5" min="0">
                        </div>
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="allowBackorders">
                                <span>السماح بالطلبات المسبقة</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="btn btn-primary" onclick="saveStoreSettings()">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                    <button class="btn btn-secondary" onclick="resetStoreSettings()">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </div>
        `;

        console.log('✅ Store Settings content loaded successfully');
    } catch (error) {
        console.error('Error loading Store Settings content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إعدادات المتجر</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadStoreSettingsContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Save store settings
 */
function saveStoreSettings() {
    // Collect form data
    const settings = {
        storeName: document.getElementById('storeName')?.value,
        storeDescription: document.getElementById('storeDescription')?.value,
        storeAddress: document.getElementById('storeAddress')?.value,
        storePhone: document.getElementById('storePhone')?.value,
        storeEmail: document.getElementById('storeEmail')?.value,
        productsPerPage: document.getElementById('productsPerPage')?.value,
        defaultProductSort: document.getElementById('defaultProductSort')?.value,
        showOutOfStock: document.getElementById('showOutOfStock')?.checked,
        enableReviews: document.getElementById('enableReviews')?.checked,
        defaultCurrency: document.getElementById('defaultCurrency')?.value,
        currencySymbol: document.getElementById('currencySymbol')?.value,
        currencyPosition: document.getElementById('currencyPosition')?.value,
        trackInventory: document.getElementById('trackInventory')?.checked,
        lowStockThreshold: document.getElementById('lowStockThreshold')?.value,
        allowBackorders: document.getElementById('allowBackorders')?.checked
    };

    // For now, just save to localStorage
    localStorage.setItem('storeSettings', JSON.stringify(settings));

    // Show success message
    if (typeof notificationManager !== 'undefined') {
        notificationManager.showSuccess('تم حفظ إعدادات المتجر بنجاح');
    } else {
        alert('تم حفظ إعدادات المتجر بنجاح');
    }
}

/**
 * Reset store settings
 */
function resetStoreSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        localStorage.removeItem('storeSettings');
        loadStoreSettingsContent(); // Reload with default values

        if (typeof notificationManager !== 'undefined') {
            notificationManager.showInfo('تم إعادة تعيين إعدادات المتجر');
        } else {
            alert('تم إعادة تعيين إعدادات المتجر');
        }
    }
}

/**
 * Load User Management content dynamically (backup function)
 */
async function loadUserManagementContentBackup() {
    console.log('Loading User Management content...');

    const container = document.getElementById('userManagementContent');
    if (!container) {
        console.error('User management container not found');
        return;
    }

    try {
        // Load the enhanced user management HTML
        const response = await fetch('user-management.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');

        // Extract the user management content
        const userManagementContent = doc.querySelector('.user-management-content');

        if (userManagementContent) {
            container.innerHTML = userManagementContent.innerHTML;

            // Also extract and append the user modals if they exist
            const userModal = doc.querySelector('#userModal');
            const userDetailsModal = doc.querySelector('#userDetailsModal');

            if (userModal && !document.getElementById('userModal')) {
                document.body.appendChild(userModal.cloneNode(true));
                console.log('✅ User modal added to document');
            }

            if (userDetailsModal && !document.getElementById('userDetailsModal')) {
                document.body.appendChild(userDetailsModal.cloneNode(true));
                console.log('✅ User details modal added to document');
            }
        } else {
            container.innerHTML = htmlContent;
        }

        console.log('User Management content loaded successfully');

        // Initialize the enhanced user management system
        if (typeof initializeUserManagement === 'function') {
            console.log('Initializing enhanced user management...');
            initializeUserManagement();
        } else {
            console.log('User Management script loaded successfully');
        }

    } catch (error) {
        console.error('Error loading User Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المستخدمين</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadUserManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}`);
        }

//         const htmlContent = await response.text(); // Fixed: await outside async function

        // Extract the user management content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const userManagementContent = doc.querySelector('.user-management-content') ||
                                     doc.querySelector('.main-content') ||
                                     doc.querySelector('body');

        if (userManagementContent) {
            container.innerHTML = userManagementContent.innerHTML;

            // Also extract and append the user modals if they exist
            const userModal = doc.querySelector('#userModal');
            const userDetailsModal = doc.querySelector('#userDetailsModal');

            if (userModal && !document.getElementById('userModal')) {
                document.body.appendChild(userModal.cloneNode(true));
                console.log('✅ User modal added to document');
            }

            if (userDetailsModal && !document.getElementById('userDetailsModal')) {
                document.body.appendChild(userDetailsModal.cloneNode(true));
                console.log('✅ User details modal added to document');
            }
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the user management JavaScript
        if (!document.querySelector('script[src="js/user-management.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/user-management.js';
            script.onload = () => {
                console.log('User Management script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load User Management script');
            };
            document.head.appendChild(script);
        }

        console.log('User Management content loaded successfully');

    } catch (error) {
        console.error('Error loading User Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المستخدمين</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadUserManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load Stores Management content dynamically
 */
async function loadStoresManagementContent() {
    const container = document.getElementById('storesManagementContent');
    if (!container) return;

    try {
        console.log('Loading Stores Management content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة المتاجر...</p>
            </div>
        `;

        // Create stores management content directly
        container.innerHTML = `
            <div class="stores-management-content">
                <div class="stores-management-header">
                    <div class="section-title-wrapper">
                        <div class="section-icon">
                            <i class="fas fa-store-alt"></i>
                        </div>
                        <div class="section-title-content">
                            <h3 class="section-title">إدارة المتاجر</h3>
                            <p class="section-subtitle">مراقبة وإدارة متاجر المستخدمين</p>
                        </div>
                    </div>
                    <div class="settings-summary">
                        <div class="summary-item">
                            <span class="summary-label">إجمالي المتاجر:</span>
                            <span class="summary-value" id="totalStores">--</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">المتاجر النشطة:</span>
                            <span class="summary-value" id="activeStores">--</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">المتاجر المعلقة:</span>
                            <span class="summary-value" id="pendingStores">--</span>
                        </div>
                    </div>
                </div>

                <div class="stores-management-controls">
                    <div class="search-filter-section">
                        <div class="search-group">
                            <input type="text" id="storeSearch" placeholder="البحث في المتاجر..." class="search-input">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <div class="filter-group">
                            <select id="statusFilter" class="filter-select">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="pending">قيد المراجعة</option>
                                <option value="blocked">محظور</option>
                                <option value="suspended">معلق</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="stores-table-container">
                    <table class="stores-table" id="storesTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAllStores" class="checkbox-input">
                                </th>
                                <th>المتجر</th>
                                <th>المالك</th>
                                <th>المنتجات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="storesTableBody">
                            <!-- Stores will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <div class="stores-pagination" id="storesPagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>
        `;

        // Initialize stores management (script is already loaded)
        console.log('Stores Management HTML loaded, initializing...');

        // Wait a bit for DOM to be ready, then initialize
        setTimeout(() => {
            if (typeof initializeStoresManagement === 'function') {
                console.log('Calling initializeStoresManagement...');
                initializeStoresManagement();
            } else {
                console.error('initializeStoresManagement function not found');
            }
        }, 200);

        console.log('Stores Management content loaded successfully');

    } catch (error) {
        console.error('Error loading Stores Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة المتاجر</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadStoresManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load Security Settings content dynamically
 */
async function loadSecuritySettingsContent() {
    const container = document.getElementById('securitySettingsContent');
    if (!container) return;

    try {
        console.log('Loading Security Settings content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إعدادات الأمان...</p>
            </div>
        `;

        // Load the security settings HTML content
        const response = await fetch('security-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract the security settings content
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const securitySettingsContent = doc.querySelector('.security-settings-content') ||
                                       doc.querySelector('.main-content') ||
                                       doc.querySelector('body');

        if (securitySettingsContent) {
            container.innerHTML = securitySettingsContent.innerHTML;
        } else {
            container.innerHTML = htmlContent;
        }

        // Load and execute the security settings JavaScript
        if (!document.querySelector('script[src="js/security-settings.js"]')) {
            const script = document.createElement('script');
            script.src = 'js/security-settings.js';
            script.onload = () => {
                console.log('Security Settings script loaded successfully');
            };
            script.onerror = () => {
                console.error('Failed to load Security Settings script');
            };
            document.head.appendChild(script);
        }

        console.log('Security Settings content loaded successfully');

    } catch (error) {
        console.error('Error loading Security Settings content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إعدادات الأمان</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadSecuritySettingsContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load Roles Management content
 */
async function loadRolesManagementContent() {
    const container = document.getElementById('rolesManagementContent');
    if (!container) return;

    try {
        console.log('Loading Roles Management content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الأدوار...</p>
            </div>
        `;

        // Create roles management interface
        container.innerHTML = `
            <div class="roles-management-content">
                <div class="roles-header">
                    <h1><i class="fas fa-user-shield"></i> إدارة الأدوار</h1>
                    <p>إدارة أدوار وصلاحيات المستخدمين في النظام</p>
                </div>

                <div class="roles-actions">
                    <button class="btn btn-primary" onclick="showAddRoleModal()">
                        <i class="fas fa-plus"></i> إضافة دور جديد
                    </button>
                    <button class="btn btn-secondary" onclick="loadRolesFromAPI()">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                </div>

                <div class="roles-list" id="rolesList">
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                        <p style="margin-top: 15px; color: #666;">جاري تحميل الأدوار...</p>
                    </div>
                </div>
            </div>
        `;

        console.log('✅ Roles Management content loaded successfully');

        // Load roles data after content is loaded
        setTimeout(() => {
            loadRolesFromAPI();
        }, 100);

    } catch (error) {
        console.error('Error loading Roles Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة الأدوار</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadRolesManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load roles from API
 */
async function loadRolesFromAPI() {
    const rolesList = document.getElementById('rolesList');
    if (!rolesList) return;

    try {
        rolesList.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل الأدوار...</p>
            </div>
        `;

        const response = await fetch('../php/api/roles.php?action=list');
        const data = await response.json();

        if (data.success) {
            displayRoles(data.roles);
        } else {
            throw new Error(data.message || 'فشل في تحميل الأدوار');
        }
    } catch (error) {
        console.error('Error loading roles:', error);
        rolesList.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل الأدوار</h4>
                <p>${error.message}</p>
                <button class="btn btn-primary" onclick="loadRolesFromAPI()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Display roles in the list
 */
function displayRoles(roles) {
    const rolesList = document.getElementById('rolesList');
    if (!rolesList) return;

    if (!roles || roles.length === 0) {
        rolesList.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-user-shield" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                <h4>لا توجد أدوار</h4>
                <p>لم يتم العثور على أي أدوار في النظام</p>
                <button class="btn btn-primary" onclick="showAddRoleModal()">
                    <i class="fas fa-plus"></i> إضافة دور جديد
                </button>
            </div>
        `;
        return;
    }

    const rolesHTML = roles.map(role => `
        <div class="role-card">
            <div class="role-header">
                <div class="role-info">
                    <h3>${role.display_name_ar}</h3>
                    <p>${role.description || 'لا يوجد وصف'}</p>
                    <span class="role-level">المستوى: ${role.level}</span>
                </div>
                <div class="role-actions">
                    <button class="btn btn-sm btn-primary" onclick="editRole(${role.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteRole(${role.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
            <div class="role-stats">
                <span class="stat">
                    <i class="fas fa-users"></i>
                    ${role.user_count || 0} مستخدم
                </span>
                <span class="stat ${role.is_active ? 'active' : 'inactive'}">
                    <i class="fas fa-${role.is_active ? 'check-circle' : 'times-circle'}"></i>
                    ${role.is_active ? 'نشط' : 'غير نشط'}
                </span>
            </div>
        </div>
    `).join('');

    rolesList.innerHTML = rolesHTML;
}

/**
 * Show add role modal
 */
function showAddRoleModal() {
    createRoleModal();
    const modal = document.getElementById('roleModal');
    const form = document.getElementById('roleForm');

    if (!modal) {
        console.error('Role modal not found');
        alert('خطأ: لم يتم العثور على نافذة إضافة الدور');
        return;
    }

    // Reset form
    if (form) {
        form.reset();
    }

    const roleId = document.getElementById('roleId');
    if (roleId) roleId.value = '';

    const modalTitle = document.getElementById('modalRoleTitle');
    if (modalTitle) modalTitle.textContent = 'إضافة دور جديد';

    // Show modal
    modal.style.display = 'flex';
}

/**
 * Edit role
 */
async function editRole(roleId) {
    try {
        // Fetch role data
        const response = await fetch(`../php/api/roles.php?action=list`);
        const data = await response.json();

        if (data.success) {
            const role = data.roles.find(r => r.id == roleId);
            if (role) {
                createRoleModal();
                const modal = document.getElementById('roleModal');

                // Populate form with role data
                document.getElementById('roleId').value = role.id;
                document.getElementById('roleName').value = role.name;
                document.getElementById('roleDisplayNameAr').value = role.display_name_ar;
                document.getElementById('roleDisplayNameEn').value = role.display_name_en;
                document.getElementById('roleDescription').value = role.description || '';
                document.getElementById('roleLevel').value = role.level;
                document.getElementById('roleStatus').checked = role.is_active;

                // Set permissions checkboxes
                const permissions = role.permissions || [];
                document.querySelectorAll('input[name="permissions"]').forEach(checkbox => {
                    checkbox.checked = permissions.includes(checkbox.value);
                });

                document.getElementById('modalRoleTitle').textContent = 'تعديل الدور';
                modal.style.display = 'flex';
            }
        }
    } catch (error) {
        console.error('Error loading role data:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في تحميل بيانات الدور');
        } else {
            alert('خطأ في تحميل بيانات الدور');
        }
    }
}

/**
 * Delete role
 */
async function deleteRole(roleId) {
    if (!confirm('هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        const response = await fetch(`../php/api/roles.php?action=delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: roleId })
        });

        const result = await response.json();

        if (result.success) {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess('تم حذف الدور بنجاح');
            } else {
                alert('تم حذف الدور بنجاح');
            }
            // Reload roles list
            loadRolesFromAPI();
        } else {
            throw new Error(result.message || 'فشل في حذف الدور');
        }
    } catch (error) {
        console.error('Error deleting role:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في حذف الدور: ' + error.message);
        } else {
            alert('خطأ في حذف الدور: ' + error.message);
        }
    }
}

/**
 * Load Subscriptions Management content
 */
async function loadSubscriptionsManagementContent() {
    const container = document.getElementById('subscriptionsManagementContent');
    if (!container) return;

    try {
        console.log('Loading Subscriptions Management content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل إدارة الاشتراكات...</p>
            </div>
        `;

        // Create subscriptions management interface
        container.innerHTML = `
            <div class="subscriptions-management-content">
                <div class="subscriptions-header">
                    <h1><i class="fas fa-crown"></i> إدارة الاشتراكات</h1>
                    <p>إدارة خطط الاشتراك وحدود المستخدمين</p>
                </div>

                <div class="subscriptions-actions">
                    <button class="btn btn-primary" onclick="showAddSubscriptionModal()">
                        <i class="fas fa-plus"></i> إضافة خطة جديدة
                    </button>
                    <button class="btn btn-secondary" onclick="loadSubscriptionsFromAPI()">
                        <i class="fas fa-sync"></i> تحديث
                    </button>
                </div>

                <div class="subscriptions-list" id="subscriptionsList">
                    <div style="text-align: center; padding: 40px;">
                        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                        <p style="margin-top: 15px; color: #666;">جاري تحميل خطط الاشتراك...</p>
                    </div>
                </div>
            </div>
        `;

        console.log('✅ Subscriptions Management content loaded successfully');

        // Load subscriptions data after content is loaded
        setTimeout(() => {
            loadSubscriptionsFromAPI();
        }, 100);

    } catch (error) {
        console.error('Error loading Subscriptions Management content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل إدارة الاشتراكات</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadSubscriptionsManagementContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Load subscriptions from API
 */
async function loadSubscriptionsFromAPI() {
    const subscriptionsList = document.getElementById('subscriptionsList');
    if (!subscriptionsList) return;

    try {
        subscriptionsList.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل خطط الاشتراك...</p>
            </div>
        `;

        const response = await fetch('../php/api/subscriptions.php?action=plans');
        const data = await response.json();

        if (data.success) {
            // Transform API data to match our display format
            const subscriptions = data.plans.map(plan => ({
                id: plan.id,
                name: plan.name,
                display_name: plan.display_name_ar,
                description: plan.description_ar || plan.description_en || '',
                price: parseFloat(plan.price),
                currency: plan.currency,
                duration_days: plan.duration_days,
                limits: {
                    products: plan.max_products,
                    landing_pages: plan.max_landing_pages,
                    storage_mb: plan.max_storage_mb
                },
                is_active: plan.is_active,
                user_count: plan.user_count || 0
            }));

            displaySubscriptions(subscriptions);
        } else {
            throw new Error(data.message || 'فشل في تحميل خطط الاشتراك');
        }
    } catch (error) {
        console.error('Error loading subscriptions:', error);
        subscriptionsList.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل خطط الاشتراك</h4>
                <p>${error.message}</p>
                <button class="btn btn-primary" onclick="loadSubscriptionsFromAPI()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Display subscriptions in the list
 */
function displaySubscriptions(subscriptions) {
    const subscriptionsList = document.getElementById('subscriptionsList');
    if (!subscriptionsList) return;

    if (!subscriptions || subscriptions.length === 0) {
        subscriptionsList.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-crown" style="font-size: 3rem; color: #ccc; margin-bottom: 15px;"></i>
                <h4>لا توجد خطط اشتراك</h4>
                <p>لم يتم العثور على أي خطط اشتراك في النظام</p>
                <button class="btn btn-primary" onclick="showAddSubscriptionModal()">
                    <i class="fas fa-plus"></i> إضافة خطة جديدة
                </button>
            </div>
        `;
        return;
    }

    const subscriptionsHTML = subscriptions.map(subscription => `
        <div class="subscription-card">
            <div class="subscription-header">
                <div class="subscription-info">
                    <h3>${subscription.display_name}</h3>
                    <p>${subscription.description}</p>
                    <div class="subscription-price">
                        ${subscription.price === 0 ? 'مجاني' : `${subscription.price} ${subscription.currency}`}
                        <span class="duration">/ ${subscription.duration_days} يوم</span>
                    </div>
                </div>
                <div class="subscription-actions">
                    <button class="btn btn-sm btn-primary" onclick="editSubscription(${subscription.id})">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSubscription(${subscription.id})">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
            <div class="subscription-limits">
                <h4>الحدود:</h4>
                <div class="limits-grid">
                    <div class="limit-item">
                        <i class="fas fa-box"></i>
                        <span>المنتجات: ${subscription.limits.products === -1 ? 'غير محدود' : subscription.limits.products}</span>
                    </div>
                    <div class="limit-item">
                        <i class="fas fa-file-alt"></i>
                        <span>صفحات الهبوط: ${subscription.limits.landing_pages === -1 ? 'غير محدود' : subscription.limits.landing_pages}</span>
                    </div>
                    <div class="limit-item">
                        <i class="fas fa-hdd"></i>
                        <span>التخزين: ${subscription.limits.storage_mb} ميجابايت</span>
                    </div>
                </div>
            </div>
            <div class="subscription-stats">
                <span class="stat">
                    <i class="fas fa-users"></i>
                    ${subscription.user_count || 0} مستخدم
                </span>
                <span class="stat ${subscription.is_active ? 'active' : 'inactive'}">
                    <i class="fas fa-${subscription.is_active ? 'check-circle' : 'times-circle'}"></i>
                    ${subscription.is_active ? 'نشط' : 'غير نشط'}
                </span>
            </div>
        </div>
    `).join('');

    subscriptionsList.innerHTML = subscriptionsHTML;
}

/**
 * Show add subscription modal
 */
function showAddSubscriptionModal() {
    createSubscriptionModal();
    const modal = document.getElementById('subscriptionModal');
    const form = document.getElementById('subscriptionForm');

    if (!modal) {
        console.error('Subscription modal not found');
        alert('خطأ: لم يتم العثور على نافذة إضافة خطة الاشتراك');
        return;
    }

    // Reset form
    if (form) {
        form.reset();
    }

    const subscriptionId = document.getElementById('subscriptionId');
    if (subscriptionId) subscriptionId.value = '';

    const modalTitle = document.getElementById('modalSubscriptionTitle');
    if (modalTitle) modalTitle.textContent = 'إضافة خطة اشتراك جديدة';

    // Show modal
    modal.style.display = 'flex';
}

/**
 * Edit subscription
 */
async function editSubscription(subscriptionId) {
    try {
        // Fetch subscription data
        const response = await fetch(`../php/api/subscriptions.php?action=plans`);
        const data = await response.json();

        if (data.success) {
            const subscription = data.plans.find(s => s.id == subscriptionId);
            if (subscription) {
                // Ensure modal exists
                createSubscriptionModal();

                // Wait a bit for DOM to be ready
                setTimeout(() => {
                    const modal = document.getElementById('subscriptionModal');

                    if (!modal) {
                        console.error('Subscription modal not found after creation');
                        alert('خطأ: لم يتم العثور على نافذة تعديل خطة الاشتراك');
                        return;
                    }

                    // Safely populate form with subscription data
                    const setElementValue = (id, value) => {
                        const element = document.getElementById(id);
                        if (element) {
                            if (element.type === 'checkbox') {
                                element.checked = value;
                            } else {
                                element.value = value || '';
                            }
                        } else {
                            console.warn(`Element with id '${id}' not found`);
                        }
                    };

                    setElementValue('subscriptionId', subscription.id);
                    setElementValue('subscriptionName', subscription.name);
                    setElementValue('subscriptionDisplayNameAr', subscription.display_name_ar);
                    setElementValue('subscriptionDisplayNameEn', subscription.display_name_en);
                    setElementValue('subscriptionDescriptionAr', subscription.description_ar);
                    setElementValue('subscriptionDescriptionEn', subscription.description_en);
                    setElementValue('subscriptionPrice', subscription.price);
                    setElementValue('subscriptionCurrency', subscription.currency);
                    setElementValue('subscriptionDuration', subscription.duration_days);
                    setElementValue('maxProducts', subscription.max_products);
                    setElementValue('maxLandingPages', subscription.max_landing_pages);
                    setElementValue('maxStorageMb', subscription.max_storage_mb);
                    setElementValue('maxTemplates', subscription.max_templates);
                    setElementValue('subscriptionStatus', subscription.is_active);

                    // Update modal title
                    const modalTitle = document.getElementById('modalSubscriptionTitle');
                    if (modalTitle) {
                        modalTitle.textContent = 'تعديل خطة الاشتراك';
                    }

                    // Show modal
                    modal.style.display = 'flex';
                }, 100); // Small delay to ensure DOM is ready
            }
        }
    } catch (error) {
        console.error('Error loading subscription data:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في تحميل بيانات خطة الاشتراك');
        } else {
            alert('خطأ في تحميل بيانات خطة الاشتراك');
        }
    }
}

/**
 * Delete subscription
 */
async function deleteSubscription(subscriptionId) {
    if (!confirm('هل أنت متأكد من حذف خطة الاشتراك هذه؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        return;
    }

    try {
        const response = await fetch(`../php/api/subscriptions.php?action=delete_plan`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: subscriptionId })
        });

        const result = await response.json();

        if (result.success) {
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showSuccess('تم حذف خطة الاشتراك بنجاح');
            } else {
                alert('تم حذف خطة الاشتراك بنجاح');
            }
            // Reload subscriptions list
            loadSubscriptionsFromAPI();
        } else {
            throw new Error(result.message || 'فشل في حذف خطة الاشتراك');
        }
    } catch (error) {
        console.error('Error deleting subscription:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.showError('خطأ في حذف خطة الاشتراك: ' + error.message);
        } else {
            alert('خطأ في حذف خطة الاشتراك: ' + error.message);
        }
    }
}

/**
 * Load System Testing content
 */
async function loadSystemTestingContent() {
    const container = document.getElementById('systemTestingContent');
    if (!container) return;

    try {
        console.log('Loading System Testing content...');

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل نظام الاختبار...</p>
            </div>
        `;

        // Create system testing interface
        container.innerHTML = `
            <div class="system-testing-content">
                <div class="testing-header">
                    <h1><i class="fas fa-vial"></i> اختبار النظام</h1>
                    <p>فحص شامل لجميع مكونات النظام والتأكد من سلامة العمل</p>
                </div>

                <div class="testing-actions">
                    <button class="btn btn-primary" onclick="runCompleteSystemTest()">
                        <i class="fas fa-play"></i> تشغيل الاختبار الشامل
                    </button>
                    <button class="btn btn-secondary" onclick="window.open('system-test.php', '_blank')">
                        <i class="fas fa-external-link-alt"></i> اختبار النظام المتقدم
                    </button>
                    <button class="btn btn-info" onclick="window.open('test-admin-sections.html', '_blank')">
                        <i class="fas fa-cogs"></i> اختبار أقسام الإدارة
                    </button>
                </div>

                <div class="testing-results" id="systemTestingResults">
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                        <h4>جاهز للاختبار</h4>
                        <p>اضغط على "تشغيل الاختبار الشامل" لبدء فحص النظام</p>
                    </div>
                </div>
            </div>
        `;

        console.log('✅ System Testing content loaded successfully');
    } catch (error) {
        console.error('Error loading System Testing content:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>خطأ في تحميل نظام الاختبار</h4>
                <p>حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</p>
                <button class="btn btn-primary" onclick="loadSystemTestingContent()">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </button>
            </div>
        `;
    }
}

/**
 * Run complete system test
 */
async function runCompleteSystemTest() {
    const resultsContainer = document.getElementById('systemTestingResults');
    if (!resultsContainer) return;

    try {
        // Show loading state
        resultsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تشغيل الاختبار الشامل...</p>
            </div>
        `;

        // Run tests
        const tests = [
            { name: 'اختبار قاعدة البيانات', endpoint: '../php/api/dashboard-stats.php' },
            { name: 'اختبار إدارة المستخدمين', endpoint: '../php/api/users.php?action=list' },
            { name: 'اختبار إدارة الأدوار', endpoint: '../php/api/roles.php?action=list' },
            { name: 'اختبار إدارة الاشتراكات', endpoint: '../php/api/subscriptions.php?action=plans' },
            { name: 'اختبار إعدادات الدفع', endpoint: '../php/api/payment-settings.php' }
        ];

        let results = [];

        for (let test of tests) {
            try {
                const response = await fetch(test.endpoint);
                const success = response.ok;
                results.push({
                    name: test.name,
                    status: success ? 'success' : 'error',
                    message: success ? 'نجح الاختبار' : `فشل الاختبار (${response.status})`
                });
            } catch (error) {
                results.push({
                    name: test.name,
                    status: 'error',
                    message: `خطأ في الاتصال: ${error.message}`
                });
            }
        }

        // Display results
        const successCount = results.filter(r => r.status === 'success').length;
        const totalCount = results.length;
        const successRate = Math.round((successCount / totalCount) * 100);

        resultsContainer.innerHTML = `
            <div class="test-results-summary">
                <h3>نتائج الاختبار الشامل</h3>
                <div class="test-summary-stats">
                    <div class="stat-item ${successRate >= 80 ? 'success' : 'warning'}">
                        <span class="stat-value">${successRate}%</span>
                        <span class="stat-label">معدل النجاح</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">${successCount}/${totalCount}</span>
                        <span class="stat-label">الاختبارات الناجحة</span>
                    </div>
                </div>
            </div>

            <div class="test-results-details">
                ${results.map(result => `
                    <div class="test-result-item ${result.status}">
                        <i class="fas ${result.status === 'success' ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                        <span class="test-name">${result.name}</span>
                        <span class="test-message">${result.message}</span>
                    </div>
                `).join('')}
            </div>
        `;

    } catch (error) {
        resultsContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 15px;"></i>
                <h4>فشل في تشغيل الاختبار</h4>
                <p>${error.message}</p>
            </div>
        `;
    }
}

// Load General Settings content
async function loadGeneralSettingsContent() {
    console.log('📡 Loading general settings content...');

    const container = document.getElementById('generalSettingsContent');
    if (!container) {
        console.error('General settings container not found');
        return;
    }

    try {
        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea;"></i>
                <p style="margin-top: 15px; color: #666;">جاري تحميل الإعدادات العامة...</p>
            </div>
        `;

        // Fetch the general settings HTML content
        const response = await fetch('general-settings.html');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const htmlContent = await response.text();

        // Extract content from the fetched HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlContent, 'text/html');
        const content = doc.querySelector('.settings-content') || doc.querySelector('main') || doc.body;

        if (content) {
            container.innerHTML = content.innerHTML;

            // Initialize any JavaScript for general settings
            if (typeof initializeGeneralSettings === 'function') {
                initializeGeneralSettings();
            }

            console.log('✅ General settings content loaded successfully');
        } else {
            throw new Error('Could not find general settings content in page');
        }

    } catch (error) {
        console.error('Error loading general settings:', error);
        container.innerHTML = `
            <div style="text-align: center; padding: 40px;">
                <i class="fas fa-cog" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
                <h3 style="color: #2c3e50; margin: 15px 0;">الإعدادات العامة</h3>
                <p style="color: #666; margin-bottom: 30px;">إدارة الإعدادات العامة للنظام</p>

                <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 800px; margin: 0 auto;">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">اسم الموقع:</label>
                        <input type="text" value="متجر الكتب" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">وصف الموقع:</label>
                        <textarea style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; height: 80px;">متجر إلكتروني متخصص في بيع الكتب والمنتجات التعليمية</textarea>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني للإدارة:</label>
                        <input type="email" value="<EMAIL>" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف:</label>
                        <input type="tel" value="+213 555 123 456" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>

                    <button onclick="saveGeneralSettings()" style="background: #667eea; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        حفظ الإعدادات
                    </button>
                </div>
            </div>
        `;
    }
}

// Load Store Settings content
async function loadStoreSettingsContent() {
    console.log('📡 Loading store settings content...');

    const container = document.getElementById('storeSettingsContent');
    if (!container) {
        console.error('Store settings container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-store" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إعدادات المتجر</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة إعدادات المتجر والعرض</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 800px; margin: 0 auto;">
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">عدد المنتجات لكل صفحة:</label>
                    <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <option value="12">12 منتج</option>
                        <option value="24">24 منتج</option>
                        <option value="36">36 منتج</option>
                    </select>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">ترتيب المنتجات الافتراضي:</label>
                    <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <option value="newest">الأحدث أولاً</option>
                        <option value="price_low">السعر من الأقل للأعلى</option>
                        <option value="price_high">السعر من الأعلى للأقل</option>
                        <option value="name">الاسم أبجدياً</option>
                    </select>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" checked>
                        <span>عرض المنتجات غير المتوفرة</span>
                    </label>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" checked>
                        <span>تفعيل نظام التقييمات</span>
                    </label>
                </div>

                <button onclick="saveStoreSettings()" style="background: #667eea; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                    حفظ الإعدادات
                </button>
            </div>
        </div>
    `;
}

// Load User Management content
async function loadUserManagementContent() {
    console.log('📡 Loading user management content...');

    const container = document.getElementById('userManagementContent');
    if (!container) {
        console.error('User management container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-users" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إدارة المستخدمين</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة حسابات المستخدمين والصلاحيات</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 1000px; margin: 0 auto;">
                <div style="margin-bottom: 20px; text-align: left;">
                    <button onclick="addNewUser()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-plus"></i> إضافة مستخدم جديد
                    </button>
                </div>

                <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الاسم</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">البريد الإلكتروني</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الدور</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الحالة</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">تاريخ التسجيل</th>
                            <th style="padding: 12px; border: 1px solid #ddd; text-align: right;">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #ddd;">أحمد محمد</td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><EMAIL></td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">مدير</span></td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">نشط</span></td>
                            <td style="padding: 12px; border: 1px solid #ddd;">2024-01-15</td>
                            <td style="padding: 12px; border: 1px solid #ddd;">
                                <button onclick="editUser(1)" style="background: #ffc107; color: black; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">تعديل</button>
                                <button onclick="deleteUser(1)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">حذف</button>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding: 12px; border: 1px solid #ddd;">فاطمة علي</td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><EMAIL></td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><span style="background: #6c757d; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">بائع</span></td>
                            <td style="padding: 12px; border: 1px solid #ddd;"><span style="background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">نشط</span></td>
                            <td style="padding: 12px; border: 1px solid #ddd;">2024-02-20</td>
                            <td style="padding: 12px; border: 1px solid #ddd;">
                                <button onclick="editUser(2)" style="background: #ffc107; color: black; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">تعديل</button>
                                <button onclick="deleteUser(2)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">حذف</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    `;
}

// Load Stores Management content
async function loadStoresManagementContent() {
    console.log('📡 Loading stores management content...');

    const container = document.getElementById('storesManagementContent');
    if (!container) {
        console.error('Stores management container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-store-alt" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إدارة المتاجر</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة متاجر البائعين والإشراف عليها</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 1200px; margin: 0 auto;">
                <div style="margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <input type="text" placeholder="البحث في المتاجر..." style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 300px;">
                    </div>
                    <button onclick="addNewStore()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                        <i class="fas fa-plus"></i> إضافة متجر جديد
                    </button>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-top: 30px;">
                    <div style="border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">متجر الكتب الأكاديمية</h4>
                        <p style="color: #666; margin: 5px 0;"><strong>المالك:</strong> أحمد محمد</p>
                        <p style="color: #666; margin: 5px 0;"><strong>المنتجات:</strong> 45 منتج</p>
                        <p style="color: #666; margin: 5px 0;"><strong>الطلبات:</strong> 128 طلب</p>
                        <p style="color: #666; margin: 5px 0;"><strong>الحالة:</strong> <span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">نشط</span></p>
                        <div style="margin-top: 15px;">
                            <button onclick="viewStore(1)" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">عرض</button>
                            <button onclick="editStore(1)" style="background: #ffc107; color: black; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">تعديل</button>
                            <button onclick="suspendStore(1)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">تعليق</button>
                        </div>
                    </div>

                    <div style="border: 1px solid #ddd; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                        <h4 style="margin: 0 0 10px 0; color: #2c3e50;">متجر الإلكترونيات</h4>
                        <p style="color: #666; margin: 5px 0;"><strong>المالك:</strong> فاطمة علي</p>
                        <p style="color: #666; margin: 5px 0;"><strong>المنتجات:</strong> 23 منتج</p>
                        <p style="color: #666; margin: 5px 0;"><strong>الطلبات:</strong> 67 طلب</p>
                        <p style="color: #666; margin: 5px 0;"><strong>الحالة:</strong> <span style="background: #28a745; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">نشط</span></p>
                        <div style="margin-top: 15px;">
                            <button onclick="viewStore(2)" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">عرض</button>
                            <button onclick="editStore(2)" style="background: #ffc107; color: black; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 5px;">تعديل</button>
                            <button onclick="suspendStore(2)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">تعليق</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Load Roles Management content
async function loadRolesManagementContent() {
    console.log('📡 Loading roles management content...');

    const container = document.getElementById('rolesManagementContent');
    if (!container) {
        console.error('Roles management container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-user-shield" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إدارة الأدوار</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة أدوار المستخدمين والصلاحيات</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 800px; margin: 0 auto;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                    <div style="border: 1px solid #007bff; border-radius: 10px; padding: 20px; background: #f8f9ff;">
                        <h4 style="color: #007bff; margin: 0 0 15px 0;"><i class="fas fa-crown"></i> مدير النظام</h4>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إدارة جميع المستخدمين</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إدارة جميع المتاجر</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إدارة النظام</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> عرض جميع التقارير</li>
                        </ul>
                        <button onclick="editRole('admin')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-top: 15px; width: 100%;">تعديل الصلاحيات</button>
                    </div>

                    <div style="border: 1px solid #6c757d; border-radius: 10px; padding: 20px; background: #f8f9fa;">
                        <h4 style="color: #6c757d; margin: 0 0 15px 0;"><i class="fas fa-store"></i> بائع</h4>
                        <ul style="list-style: none; padding: 0; margin: 0;">
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إدارة منتجاته فقط</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إدارة طلباته فقط</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> إنشاء صفحات هبوط</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-times text-danger"></i> إدارة المستخدمين</li>
                        </ul>
                        <button onclick="editRole('seller')" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; margin-top: 15px; width: 100%;">تعديل الصلاحيات</button>
                    </div>
                </div>

                <div style="margin-top: 30px; text-align: center;">
                    <button onclick="addNewRole()" style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-plus"></i> إضافة دور جديد
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Load Subscriptions Management content
async function loadSubscriptionsManagementContent() {
    console.log('📡 Loading subscriptions management content...');

    const container = document.getElementById('subscriptionsManagementContent');
    if (!container) {
        console.error('Subscriptions management container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-credit-card" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إدارة الاشتراكات</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة خطط الاشتراك وحدود المستخدمين</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 1000px; margin: 0 auto;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="border: 1px solid #28a745; border-radius: 10px; padding: 20px; background: #f8fff8;">
                        <h4 style="color: #28a745; margin: 0 0 15px 0;"><i class="fas fa-seedling"></i> الخطة الأساسية</h4>
                        <p style="font-size: 24px; font-weight: bold; color: #2c3e50; margin: 10px 0;">مجاني</p>
                        <ul style="list-style: none; padding: 0; margin: 15px 0;">
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> 5 منتجات كحد أقصى</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> 2 صفحة هبوط</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> دعم أساسي</li>
                        </ul>
                        <p style="color: #666; margin: 10px 0;"><strong>المشتركون:</strong> 45 مستخدم</p>
                        <button onclick="editPlan('basic')" style="background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; width: 100%;">تعديل الخطة</button>
                    </div>

                    <div style="border: 1px solid #007bff; border-radius: 10px; padding: 20px; background: #f8f9ff;">
                        <h4 style="color: #007bff; margin: 0 0 15px 0;"><i class="fas fa-rocket"></i> الخطة المتقدمة</h4>
                        <p style="font-size: 24px; font-weight: bold; color: #2c3e50; margin: 10px 0;">2000 دج/شهر</p>
                        <ul style="list-style: none; padding: 0; margin: 15px 0;">
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> 50 منتج كحد أقصى</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> 10 صفحات هبوط</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> دعم متقدم</li>
                        </ul>
                        <p style="color: #666; margin: 10px 0;"><strong>المشتركون:</strong> 12 مستخدم</p>
                        <button onclick="editPlan('premium')" style="background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; width: 100%;">تعديل الخطة</button>
                    </div>

                    <div style="border: 1px solid #ffc107; border-radius: 10px; padding: 20px; background: #fffdf8;">
                        <h4 style="color: #ffc107; margin: 0 0 15px 0;"><i class="fas fa-crown"></i> الخطة الاحترافية</h4>
                        <p style="font-size: 24px; font-weight: bold; color: #2c3e50; margin: 10px 0;">5000 دج/شهر</p>
                        <ul style="list-style: none; padding: 0; margin: 15px 0;">
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> منتجات غير محدودة</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> صفحات هبوط غير محدودة</li>
                            <li style="padding: 5px 0; color: #666;"><i class="fas fa-check text-success"></i> دعم أولوية</li>
                        </ul>
                        <p style="color: #666; margin: 10px 0;"><strong>المشتركون:</strong> 3 مستخدم</p>
                        <button onclick="editPlan('professional')" style="background: #ffc107; color: black; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; width: 100%;">تعديل الخطة</button>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button onclick="addNewPlan()" style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;">
                        <i class="fas fa-plus"></i> إضافة خطة جديدة
                    </button>
                    <button onclick="viewSubscriptionReports()" style="background: #17a2b8; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-chart-bar"></i> تقارير الاشتراكات
                    </button>
                </div>
            </div>
        </div>
    `;
}

// Load Security Settings content
async function loadSecuritySettingsContent() {
    console.log('📡 Loading security settings content...');

    const container = document.getElementById('securitySettingsContent');
    if (!container) {
        console.error('Security settings container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-shield-alt" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">إعدادات الأمان</h3>
            <p style="color: #666; margin-bottom: 30px;">إدارة إعدادات الأمان والحماية</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 800px; margin: 0 auto;">
                <div style="margin-bottom: 30px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;"><i class="fas fa-key"></i> إعدادات كلمة المرور</h4>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked>
                            <span>طلب كلمة مرور قوية (8 أحرف على الأقل)</span>
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked>
                            <span>طلب أرقام ورموز في كلمة المرور</span>
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">انتهاء صلاحية كلمة المرور (بالأيام):</label>
                        <input type="number" value="90" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                </div>

                <div style="margin-bottom: 30px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;"><i class="fas fa-lock"></i> إعدادات تسجيل الدخول</h4>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">عدد محاولات تسجيل الدخول المسموحة:</label>
                        <input type="number" value="5" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">مدة الحظر بعد المحاولات الفاشلة (بالدقائق):</label>
                        <input type="number" value="30" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked>
                            <span>تفعيل المصادقة الثنائية</span>
                        </label>
                    </div>
                </div>

                <div style="margin-bottom: 30px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;"><i class="fas fa-history"></i> سجل الأنشطة</h4>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked>
                            <span>تسجيل عمليات تسجيل الدخول</span>
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" checked>
                            <span>تسجيل تغييرات البيانات</span>
                        </label>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">مدة الاحتفاظ بالسجلات (بالأيام):</label>
                        <input type="number" value="365" style="width: 100px; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                </div>

                <button onclick="saveSecuritySettings()" style="background: #667eea; color: white; border: none; padding: 12px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                    حفظ إعدادات الأمان
                </button>
            </div>
        </div>
    `;
}

// Load System Testing content
async function loadSystemTestingContent() {
    console.log('📡 Loading system testing content...');

    const container = document.getElementById('systemTestingContent');
    if (!container) {
        console.error('System testing container not found');
        return;
    }

    container.innerHTML = `
        <div style="text-align: center; padding: 40px;">
            <i class="fas fa-vial" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
            <h3 style="color: #2c3e50; margin: 15px 0;">نظام الاختبار</h3>
            <p style="color: #666; margin-bottom: 30px;">اختبار وظائف النظام والتحقق من سلامة العمليات</p>

            <div style="background: #fff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: right; max-width: 800px; margin: 0 auto;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="border: 1px solid #007bff; border-radius: 10px; padding: 20px; background: #f8f9ff;">
                        <h4 style="color: #007bff; margin: 0 0 15px 0;"><i class="fas fa-database"></i> اختبار قاعدة البيانات</h4>
                        <p style="color: #666; margin-bottom: 15px;">فحص الاتصال بقاعدة البيانات والجداول</p>
                        <button onclick="testDatabase()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                            تشغيل الاختبار
                        </button>
                        <div id="dbTestResult" style="margin-top: 10px; padding: 10px; border-radius: 5px; display: none;"></div>
                    </div>

                    <div style="border: 1px solid #28a745; border-radius: 10px; padding: 20px; background: #f8fff8;">
                        <h4 style="color: #28a745; margin: 0 0 15px 0;"><i class="fas fa-plug"></i> اختبار APIs</h4>
                        <p style="color: #666; margin-bottom: 15px;">فحص جميع نقاط الاتصال والواجهات</p>
                        <button onclick="testAPIs()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                            تشغيل الاختبار
                        </button>
                        <div id="apiTestResult" style="margin-top: 10px; padding: 10px; border-radius: 5px; display: none;"></div>
                    </div>

                    <div style="border: 1px solid #ffc107; border-radius: 10px; padding: 20px; background: #fffdf8;">
                        <h4 style="color: #ffc107; margin: 0 0 15px 0;"><i class="fas fa-shield-alt"></i> اختبار الأمان</h4>
                        <p style="color: #666; margin-bottom: 15px;">فحص إعدادات الأمان والحماية</p>
                        <button onclick="testSecurity()" style="background: #ffc107; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                            تشغيل الاختبار
                        </button>
                        <div id="securityTestResult" style="margin-top: 10px; padding: 10px; border-radius: 5px; display: none;"></div>
                    </div>

                    <div style="border: 1px solid #dc3545; border-radius: 10px; padding: 20px; background: #fff8f8;">
                        <h4 style="color: #dc3545; margin: 0 0 15px 0;"><i class="fas fa-tachometer-alt"></i> اختبار الأداء</h4>
                        <p style="color: #666; margin-bottom: 15px;">قياس سرعة الاستجابة والأداء</p>
                        <button onclick="testPerformance()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; width: 100%;">
                            تشغيل الاختبار
                        </button>
                        <div id="performanceTestResult" style="margin-top: 10px; padding: 10px; border-radius: 5px; display: none;"></div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button onclick="runAllTests()" style="background: #667eea; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px; margin-left: 10px;">
                        <i class="fas fa-play"></i> تشغيل جميع الاختبارات
                    </button>
                    <button onclick="generateTestReport()" style="background: #17a2b8; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;">
                        <i class="fas fa-file-alt"></i> إنشاء تقرير
                    </button>
                </div>

                <div id="allTestsResult" style="margin-top: 30px; padding: 20px; border-radius: 10px; display: none;"></div>
            </div>
        </div>
    `;
}

// Register full implementations (these will override the fallback functions)
window.editProductFull = editProduct;
window.viewProductFull = viewProduct;
window.deleteProductFull = deleteProduct;
window.viewLandingPageFull = viewLandingPage;

// Override the fallback functions with full implementations
window.editProduct = editProduct;
window.viewProduct = viewProduct;
window.deleteProduct = deleteProduct;
window.viewLandingPage = viewLandingPage;
window.closeProductViewModal = closeProductViewModal;
window.showProductViewModal = showProductViewModal;

// Mark that full implementations are loaded
window.productManagementFullyLoaded = true;

// Make other admin functions globally available
window.loadLandingPages = loadLandingPages;
window.initializeLandingPagesData = initializeLandingPagesData;
window.renderLandingPagesPage = renderLandingPagesPage;
window.changeLandingPagesPerPage = changeLandingPagesPerPage;
window.searchLandingPages = searchLandingPages;
window.filterLandingPagesByStatus = filterLandingPagesByStatus;
window.filterLandingPagesByOwner = filterLandingPagesByOwner;
window.goToLandingPagesPage = goToLandingPagesPage;
window.previousLandingPagesPage = previousLandingPagesPage;
window.nextLandingPagesPage = nextLandingPagesPage;
window.addNewLandingPage = addNewLandingPage;
window.editLandingPage = editLandingPage;
window.toggleLandingPageStatus = toggleLandingPageStatus;
window.deleteLandingPage = deleteLandingPage;

// Make settings functions globally available
window.loadGeneralSettingsContent = loadGeneralSettingsContent;
window.loadStoreSettingsContent = loadStoreSettingsContent;
window.loadUserManagementContent = loadUserManagementContent;
window.loadStoresManagementContent = loadStoresManagementContent;
window.loadRolesManagementContent = loadRolesManagementContent;
window.loadSubscriptionsManagementContent = loadSubscriptionsManagementContent;
window.loadSecuritySettingsContent = loadSecuritySettingsContent;
window.loadSystemTestingContent = loadSystemTestingContent;
window.loadReportsContent = loadReportsContent;
window.showReportsError = showReportsError;

console.log('🚀 Enhanced Admin system with multi-user support loaded successfully');
console.log('✅ All product management functions are now globally available:');
console.log('   - editProduct(id)');
console.log('   - viewProduct(id)');
console.log('   - deleteProduct(id)');
console.log('   - viewLandingPage(id)');
console.log('✅ Multi-user architecture with proper access control enabled');
console.log('✅ Arabic RTL layout and professional UI/UX maintained');
