[14-Jul-2025 16:36:24 UTC] AI API: Action = get_config
[14-Jul-2025 16:36:24 UTC] AI API: Request method = GET
[14-Jul-2025 16:36:24 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 16:36:24 UTC] AI API: POST params = []
[14-Jul-2025 16:38:07 UTC] AI API: Action = get_config
[14-Jul-2025 16:38:07 UTC] AI API: Request method = GET
[14-Jul-2025 16:38:07 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 16:38:07 UTC] AI API: POST params = []
[14-Jul-2025 16:46:08 UTC] AI API: Action = get_config
[14-Jul-2025 16:46:08 UTC] AI API: Request method = GET
[14-Jul-2025 16:46:08 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 16:46:08 UTC] AI API: POST params = []
[14-Jul-2025 16:46:14 UTC] AI API: Action = get_config
[14-Jul-2025 16:46:14 UTC] AI API: Request method = GET
[14-Jul-2025 16:46:14 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 16:46:14 UTC] AI API: POST params = []
[14-Jul-2025 16:49:02 UTC] AI API: Action = get_config
[14-Jul-2025 16:49:02 UTC] AI API: Request method = GET
[14-Jul-2025 16:49:02 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 16:49:02 UTC] AI API: POST params = []
[14-Jul-2025 17:04:28 UTC] AI API: Action = get_config
[14-Jul-2025 17:04:28 UTC] AI API: Request method = GET
[14-Jul-2025 17:04:28 UTC] AI API: GET params = {"action":"get_config"}
[14-Jul-2025 17:04:28 UTC] AI API: POST params = []
[14-Jul-2025 17:44:01 UTC] Call to undefined method Security::init()
[14-Jul-2025 18:04:02 UTC] Call to undefined method Security::init()
[14-Jul-2025 18:13:35 UTC] Call to undefined method Security::init()
[14-Jul-2025 21:47:00 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:03:38 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:03:54 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:04:50 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:21:03 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:21:25 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:27:13 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:40:42 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:41:02 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:47:29 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:49:57 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:54:26 UTC] Call to undefined method Security::init()
[14-Jul-2025 22:54:40 UTC] Call to undefined method Security::init()
[15-Jul-2025 19:07:13 UTC] Call to undefined method Security::init()
[15-Jul-2025 19:49:06 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:49:34 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:51:09 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:51:35 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:57:52 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:57:59 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:01 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:01 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:07 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:08 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:09 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 19:58:27 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:01:31 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:06:21 UTC] AI Settings Error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_tested' in 'field list'
[15-Jul-2025 20:06:21 UTC] AI Settings Error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_tested' in 'field list'
[15-Jul-2025 20:08:13 UTC] AI Settings Error: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'last_tested' in 'field list'
[15-Jul-2025 20:28:26 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:28 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:29 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:33 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:36 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:38 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:28:59 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 20:33:01 UTC] AI API Error: إجراء غير صالح: 
[15-Jul-2025 20:45:07 UTC] AI API: Action = generate_product_description
[15-Jul-2025 20:45:07 UTC] AI API: Request method = POST
[15-Jul-2025 20:45:07 UTC] AI API: GET params = []
[15-Jul-2025 20:45:07 UTC] AI API: POST params = []
[15-Jul-2025 20:45:07 UTC] Call to undefined method PDO::isProviderEnabled()
[15-Jul-2025 20:58:31 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 20:58:34 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 20:59:07 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 20:59:14 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 20:59:19 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 22:56:50 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 22:56:51 UTC] AI API Error: عنوان المنتج مطلوب
[15-Jul-2025 22:58:18 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 23:04:55 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 23:06:36 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[15-Jul-2025 23:07:09 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 00:14:06 UTC] AI API Error: عنوان المنتج مطلوب
[16-Jul-2025 12:28:25 UTC] PHP Warning:  require_once(../debug.php): Failed to open stream: No such file or directory in K:\Projets_Sites_Web\Mossaab-LandingPage\php\api\categories.php on line 13
[16-Jul-2025 12:28:25 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:29:44 UTC] Users API Error: Invalid action
[16-Jul-2025 12:29:44 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:29:59 UTC] Roles API Error: Invalid action
[16-Jul-2025 12:29:59 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:38:51 UTC] Subscriptions API Error: Invalid action
[16-Jul-2025 12:38:52 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:46:06 UTC] Users API Error: Invalid action
[16-Jul-2025 12:46:06 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:46:39 UTC] Roles API Error: Invalid action
[16-Jul-2025 12:46:39 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:47:02 UTC] Roles API Error: Invalid action
[16-Jul-2025 12:47:02 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 12:47:05 UTC] Subscriptions API Error: Invalid action
[16-Jul-2025 12:47:05 UTC] PHP Fatal error:  Cannot redeclare class Config (previously declared in K:\Projets_Sites_Web\Mossaab-LandingPage\config\config.php:8) in K:\Projets_Sites_Web\Mossaab-LandingPage\php\config.php on line 7
[16-Jul-2025 13:07:31 UTC] Users API Error: Invalid action
[16-Jul-2025 13:07:31 UTC] Call to undefined method Config::getInstance()
[16-Jul-2025 13:17:39 UTC] Roles API Error: Invalid action
[16-Jul-2025 13:17:39 UTC] Call to undefined method Config::getInstance()
[16-Jul-2025 16:37:10 UTC] AI API Error: عنوان المنتج مطلوب
[16-Jul-2025 16:37:21 UTC] AI API Error: عنوان المنتج مطلوب
[16-Jul-2025 16:37:56 UTC] AI API Error: عنوان المنتج مطلوب
[16-Jul-2025 16:38:12 UTC] AI API Error: عنوان المنتج مطلوب
