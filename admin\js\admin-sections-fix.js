/**
 * Admin Sections Fix - Missing Load Functions
 * إصلاح الوظائف المفقودة لتحميل محتوى الأقسام
 */

(function() {
    "use strict";
    
    console.log("🔧 Admin Sections Fix loading...");
    
    // Fix for Dashboard Content Loading
    if (typeof loadDashboardContent === "undefined") {
        window.loadDashboardContent = function() {
            console.log("📊 Loading Dashboard Content...");
            
            const dashboardContent = document.getElementById("dashboardContent");
            if (!dashboardContent) {
                console.error("Dashboard content container not found");
                return;
            }
            
            // Show dashboard content
            dashboardContent.style.display = "block";
            dashboardContent.style.visibility = "visible";
            dashboardContent.style.opacity = "1";
            
            // Load dashboard data if loadDashboard function exists
            if (typeof loadDashboard === "function") {
                loadDashboard();
            } else {
                // Create basic dashboard content
                dashboardContent.innerHTML = `
                    <div class="dashboard-header">
                        <h2><i class="fas fa-tachometer-alt"></i> لوحة المعلومات</h2>
                    </div>
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <h3>إحصائيات النظام</h3>
                            <p>مرحباً بك في لوحة التحكم</p>
                        </div>
                    </div>
                `;
            }
            
            console.log("✅ Dashboard content loaded");
        };
    }
    
    // Fix for Products Content Loading
    if (typeof loadProductsContent === "undefined") {
        window.loadProductsContent = function() {
            console.log("📦 Loading Products Content...");
            
            const productsContent = document.getElementById("productsContent");
            if (!productsContent) {
                console.error("Products content container not found");
                return;
            }
            
            // Show products content
            productsContent.style.display = "block";
            productsContent.style.visibility = "visible";
            productsContent.style.opacity = "1";
            
            // Load products data if loadProducts function exists
            if (typeof loadProducts === "function") {
                loadProducts();
            } else {
                // Create basic products content
                productsContent.innerHTML = `
                    <div class="products-header">
                        <h2><i class="fas fa-box"></i> إدارة المنتجات</h2>
                        <button class="btn btn-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i> إضافة منتج جديد
                        </button>
                    </div>
                    <div class="products-list">
                        <p>جاري تحميل المنتجات...</p>
                    </div>
                `;
            }
            
            console.log("✅ Products content loaded");
        };
    }
    
    // Fix for Orders Content Loading
    if (typeof loadOrdersContent === "undefined") {
        window.loadOrdersContent = function() {
            console.log("🛒 Loading Orders Content...");
            
            const ordersContent = document.getElementById("ordersContent");
            if (!ordersContent) {
                console.error("Orders content container not found");
                return;
            }
            
            // Show orders content
            ordersContent.style.display = "block";
            ordersContent.style.visibility = "visible";
            ordersContent.style.opacity = "1";
            
            // Load orders data if loadOrders function exists
            if (typeof loadOrders === "function") {
                loadOrders();
            } else {
                // Create basic orders content
                ordersContent.innerHTML = `
                    <div class="orders-header">
                        <h2><i class="fas fa-shopping-cart"></i> إدارة الطلبات</h2>
                    </div>
                    <div class="orders-list">
                        <p>جاري تحميل الطلبات...</p>
                    </div>
                `;
            }
            
            console.log("✅ Orders content loaded");
        };
    }
    
    // Fix for Landing Pages Content Loading
    if (typeof loadLandingPagesContent === "undefined") {
        window.loadLandingPagesContent = function() {
            console.log("🚀 Loading Landing Pages Content...");
            
            const landingPagesContent = document.getElementById("landingPagesContent");
            if (!landingPagesContent) {
                console.error("Landing Pages content container not found");
                return;
            }
            
            // Show landing pages content
            landingPagesContent.style.display = "block";
            landingPagesContent.style.visibility = "visible";
            landingPagesContent.style.opacity = "1";
            
            // Initialize landing pages manager if available
            if (typeof landingPagesManager !== "undefined" && landingPagesManager.init) {
                landingPagesManager.init();
            }
            
            console.log("✅ Landing Pages content loaded");
        };
    }
    
    // Fix for Settings Content Loading
    if (typeof loadSettingsContent === "undefined") {
        window.loadSettingsContent = function() {
            console.log("⚙️ Loading Settings Content...");
            
            const settingsContent = document.getElementById("settingsContent");
            if (!settingsContent) {
                console.error("Settings content container not found");
                return;
            }
            
            // Show settings content
            settingsContent.style.display = "block";
            settingsContent.style.visibility = "visible";
            settingsContent.style.opacity = "1";
            
            // Create basic settings content if not exists
            if (!settingsContent.innerHTML.trim()) {
                settingsContent.innerHTML = `
                    <div class="settings-header">
                        <h2><i class="fas fa-cog"></i> إعدادات النظام</h2>
                    </div>
                    <div class="settings-tabs">
                        <div class="tab-content">
                            <p>إعدادات النظام متاحة هنا</p>
                        </div>
                    </div>
                `;
            }
            
            console.log("✅ Settings content loaded");
        };
    }
    
    // Enhanced Section Navigation Handler
    function enhancedSectionNavigation() {
        console.log("🔗 Setting up enhanced section navigation...");
        
        // Find all navigation items
        const navItems = document.querySelectorAll(".admin-nav ul li, .sidebar-item, .nav-item");
        
        navItems.forEach(item => {
            // Skip if already has click handler
            if (item.hasAttribute("data-enhanced-nav")) return;
            
            item.setAttribute("data-enhanced-nav", "true");
            
            item.addEventListener("click", function(e) {
                e.preventDefault();
                
                const sectionId = this.getAttribute("data-section") || 
                                this.getAttribute("data-target") || 
                                this.getAttribute("onclick")?.match(/load(\w+)/)?.[1]?.toLowerCase();
                
                if (!sectionId) return;
                
                console.log("🔄 Enhanced navigation to section:", sectionId);
                
                // Hide all content sections
                document.querySelectorAll(".content-section").forEach(section => {
                    section.classList.remove("active");
                    section.style.display = "none";
                });
                
                // Remove active class from all nav items
                document.querySelectorAll(".admin-nav ul li").forEach(navItem => {
                    navItem.classList.remove("active");
                });
                
                // Add active class to clicked item
                this.classList.add("active");
                
                // Show target section and load content
                const targetSection = document.getElementById(sectionId + "Content") || 
                                    document.getElementById(sectionId);
                
                if (targetSection) {
                    targetSection.classList.add("active");
                    targetSection.style.display = "block";
                    targetSection.style.visibility = "visible";
                    targetSection.style.opacity = "1";
                    
                    // Load section-specific content
                    switch(sectionId) {
                        case "dashboard":
                            if (typeof loadDashboardContent === "function") loadDashboardContent();
                            break;
                        case "products":
                        case "books":
                            if (typeof loadProductsContent === "function") loadProductsContent();
                            break;
                        case "orders":
                            if (typeof loadOrdersContent === "function") loadOrdersContent();
                            break;
                        case "landingPages":
                        case "landing-pages":
                            if (typeof loadLandingPagesContent === "function") loadLandingPagesContent();
                            break;
                        case "settings":
                            if (typeof loadSettingsContent === "function") loadSettingsContent();
                            break;
                        case "reports":
                            if (typeof loadReportsContent === "function") loadReportsContent();
                            break;
                    }
                    
                    console.log("✅ Section loaded:", sectionId);
                } else {
                    console.warn("⚠️ Target section not found:", sectionId);
                }
            });
        });
        
        console.log("✅ Enhanced section navigation setup complete");
    }
    
    // Initialize when DOM is ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", enhancedSectionNavigation);
    } else {
        enhancedSectionNavigation();
    }
    
    // Also run after a short delay to catch dynamically added elements
    setTimeout(enhancedSectionNavigation, 2000);
    
    console.log("✅ Admin Sections Fix loaded successfully");
    
})();