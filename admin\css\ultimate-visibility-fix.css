/* Ultimate UI Visibility Fix */

/* Force all admin sections to be visible */
.admin-section,
.content-section,
#landingPagesContent,
.landing-pages-section {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
}

/* Show active sections */
.content-section.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Force buttons to be visible */
#addLandingPageBtn,
.add-landing-page-btn,
.action-button,
[data-action="add-landing-page"] {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 1 !important;
    min-width: 120px !important;
    min-height: 36px !important;
    padding: 8px 16px !important;
    margin: 5px !important;
    background-color: #007bff !important;
    color: white !important;
    border: 1px solid #007bff !important;
    border-radius: 4px !important;
    cursor: pointer !important;
}

/* Fix loading states */
.loading {
    display: none !important;
}

.loaded {
    display: block !important;
    visibility: visible !important;
}

/* Force main content to show */
.main-content {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Fix sidebar */
.sidebar {
    display: block !important;
    visibility: visible !important;
}

/* Fix modal visibility */
.modal {
    z-index: 1050 !important;
}

.modal.show {
    display: block !important;
    opacity: 1 !important;
}

/* Fix for hidden containers */
.container,
.admin-container {
    display: block !important;
    visibility: visible !important;
}

/* Debug helper */
.force-visible {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 999 !important;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .admin-section,
    .content-section {
        display: block !important;
        width: 100% !important;
    }
}