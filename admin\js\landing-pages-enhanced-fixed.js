/**
 * Enhanced Landing Pages Manager with Fixed UI Visibility
 * Fixes all JavaScript errors and UI visibility issues
 */

(function() {
    "use strict";

    console.log("🚀 Enhanced Landing Pages Manager loading...");

    // Enhanced Landing Pages Manager
    const enhancedLandingPagesManager = {
        initialized: false,
        modal: null,
        addButton: null,

        // Initialize the manager
        async init() {
            console.log("🔧 Initializing Enhanced Landing Pages Manager...");

            try {
                // Wait for DOM to be ready
                if (document.readyState === "loading") {
                    await new Promise(resolve => {
                        document.addEventListener("DOMContentLoaded", resolve);
                    });
                }

                // Find elements
                this.findElements();

                // Setup event listeners
                this.setupEventListeners();

                // Load templates
                await this.loadTemplates();

                // Ensure UI is visible
                this.ensureUIVisibility();

                this.initialized = true;
                console.log("✅ Enhanced Landing Pages Manager initialized successfully");

            } catch (error) {
                console.error("❌ Failed to initialize Enhanced Landing Pages Manager:", error);
            }
        },

        // Find required DOM elements
        findElements() {
            console.log("🔍 Finding DOM elements...");

            // Find add button with multiple selectors
            const buttonSelectors = [
                "#addLandingPageBtn",
                "[data-action=\"add-landing-page\"]",
                ".add-landing-page-btn",
                "button[onclick*=\"landing\"]"
            ];

            for (const selector of buttonSelectors) {
                this.addButton = document.querySelector(selector);
                if (this.addButton) {
                    console.log(`✅ Add button found with selector: ${selector}`);
                    break;
                }
            }

            if (!this.addButton) {
                console.log("⚠️ Add button not found, creating one...");
                this.createAddButton();
            }

            // Find or create modal
            this.modal = document.getElementById("landingPageModal");
            if (!this.modal) {
                console.log("⚠️ Modal not found, creating one...");
                this.createModal();
            }
        },

        // Create add button if not found
        createAddButton() {
            const container = document.querySelector(".landing-pages-section") ||
                            document.querySelector("#landingPagesContent") ||
                            document.querySelector(".content-section.active") ||
                            document.body;

            const button = document.createElement("button");
            button.id = "addLandingPageBtn";
            button.className = "btn btn-primary";
            button.innerHTML = "<i class=\"fas fa-plus\"></i> أَضف صفحة هبوط";
            button.style.cssText = "display: inline-block !important; visibility: visible !important; margin: 10px;";

            container.appendChild(button);
            this.addButton = button;

            console.log("✅ Add button created successfully");
        },

        // Create modal if not found
        createModal() {
            const modalHTML = `
                <div id="landingPageModal" class="modal fade" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">إنشاء صفحة هبوط جديدة</h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <form id="landingPageForm">
                                    <div class="form-group">
                                        <label for="landingPageTitle">عنوان صفحة الهبوط</label>
                                        <input type="text" class="form-control" id="landingPageTitle" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="productSelect">اختر المنتج</label>
                                        <select class="form-control" id="productSelect" required>
                                            <option value="">اختر منتج...</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="templateSelect">اختر القالب</label>
                                        <select class="form-control" id="templateSelect" required>
                                            <option value="default">القالب الافتراضي</option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                                <button type="button" class="btn btn-primary" id="saveLandingPageBtn">حفظ</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML("beforeend", modalHTML);
            this.modal = document.getElementById("landingPageModal");

            console.log("✅ Modal created successfully");
        },

        // Setup event listeners
        setupEventListeners() {
            console.log("🔗 Setting up event listeners...");

            // Add button click handler
            if (this.addButton) {
                this.addButton.addEventListener("click", (e) => {
                    e.preventDefault();
                    console.log("🖱️ Add button clicked!");
                    this.openModal();
                });

                console.log("✅ Add button event listener attached");
            }

            // Global event delegation for dynamically created buttons
            document.addEventListener("click", (e) => {
                if (e.target.matches("#addLandingPageBtn, .add-landing-page-btn, [data-action=\"add-landing-page\"]")) {
                    e.preventDefault();
                    console.log("🖱️ Add button clicked via delegation!");
                    this.openModal();
                }
            });

            console.log("✅ Event listeners setup complete");
        },

        // Load templates from API
        async loadTemplates() {
            console.log("📋 Loading templates...");

            try {
                const response = await fetch("/php/api/templates.php?action=get_templates");

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.templates) {
                    console.log(`✅ Loaded ${data.templates.length} templates`);
                    this.templates = data.templates;
                } else {
                    console.warn("⚠️ No templates loaded, using defaults");
                    this.templates = [
                        { id: "default", name: "القالب الافتراضي" },
                        { id: "modern", name: "قالب عصري" },
                        { id: "classic", name: "قالب كلاسيكي" }
                    ];
                }

                this.updateTemplateSelect();

            } catch (error) {
                console.error("❌ Failed to load templates:", error);
                this.templates = [{ id: "default", name: "القالب الافتراضي" }];
                this.updateTemplateSelect();
            }
        },

        // Update template select options
        updateTemplateSelect() {
            const templateSelect = document.getElementById("templateSelect");
            if (templateSelect && this.templates) {
                templateSelect.innerHTML = "";

                this.templates.forEach(template => {
                    const option = document.createElement("option");
                    option.value = template.id;
                    option.textContent = template.name;
                    templateSelect.appendChild(option);
                });

                console.log("✅ Template select updated");
            }
        },

        // Ensure UI elements are visible
        ensureUIVisibility() {
            console.log("👁️ Ensuring UI visibility...");

            // Make sure add button is visible
            if (this.addButton) {
                this.addButton.style.cssText = "display: inline-block !important; visibility: visible !important;";

                // Make sure parent containers are visible
                let parent = this.addButton.parentElement;
                while (parent && parent !== document.body) {
                    if (parent.style.display === "none") {
                        parent.style.display = "block";
                        console.log("✅ Made parent container visible:", parent.className);
                    }
                    parent = parent.parentElement;
                }
            }

            // Activate landing pages section if it exists
            const landingPagesSection = document.querySelector("[data-section=\"landing-pages\"]") ||
                                      document.querySelector("#landingPagesContent") ||
                                      document.querySelector(".landing-pages-section");

            if (landingPagesSection) {
                landingPagesSection.style.display = "block";
                landingPagesSection.classList.add("active");
                console.log("✅ Landing pages section activated");
            }

            console.log("✅ UI visibility ensured");
        },

        // Open modal
        openModal() {
            console.log("📝 Opening landing page modal...");

            if (this.modal) {
                // Show modal
                this.modal.style.display = "block";
                this.modal.classList.add("show");

                // Add backdrop
                if (!document.querySelector(".modal-backdrop")) {
                    const backdrop = document.createElement("div");
                    backdrop.className = "modal-backdrop fade show";
                    document.body.appendChild(backdrop);
                }

                document.body.classList.add("modal-open");

                console.log("✅ Modal opened successfully");
            } else {
                console.error("❌ Modal not found");
                alert("خطأ: نافذة إنشاء صفحة الهبوط غير متاحة");
            }
        },

        // Close modal
        closeModal() {
            console.log("❌ Closing landing page modal...");

            if (this.modal) {
                this.modal.style.display = "none";
                this.modal.classList.remove("show");

                // Remove backdrop
                const backdrop = document.querySelector(".modal-backdrop");
                if (backdrop) {
                    backdrop.remove();
                }

                document.body.classList.remove("modal-open");

                console.log("✅ Modal closed successfully");
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", () => {
            enhancedLandingPagesManager.init();
        });
    } else {
        enhancedLandingPagesManager.init();
    }

    // Make manager globally available
    window.enhancedLandingPagesManager = enhancedLandingPagesManager;

    // Compatibility functions
    window.openLandingPageModal = () => enhancedLandingPagesManager.openModal();
    window.safeAddLandingPage = () => enhancedLandingPagesManager.openModal();

    console.log("✅ Enhanced Landing Pages Manager loaded successfully");

})();
